# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# database
/prisma/db.sqlite
/prisma/db.sqlite-journal

# next.js
/.next/
/out/
next-env.d.ts

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
# do not commit any .env files to git, except for the .env.example file. https://create.t3.gg/en/usage/env-variables#using-environment-variables
.env
peter.env.local

# vercel
.vercel

# typescript
*.tsbuildinfo
/docker_emp/data/
/prisma/client/
/prisma/generated/
/prisma/prismaMariaDB/client/
/prisma/prismaMariaDB/migrations/
/docker_emp/data/mongo-1/
/docker_emp/data/mongo-2/
/docker_emp/data/mongo-3/
.backup
.vscode/settings.json
OCPI-2.2.1.pdf
