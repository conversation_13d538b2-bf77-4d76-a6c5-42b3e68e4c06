{"welcome": "Welcome!", "goodbye": "Goodbye", "thank_you": "Thank you", "no_price_found": "No price found for this location.", "try_another_qr": "Please try again with another QR code.", "agree": "Agree", "ad_hoc_tariff": "Ad Hoc Charging Tariff", "start_fee": "Starting Fee", "terms_of_condition": "Our <0>terms and conditions of use </0> and our <1>privacy policy</1> apply.", "prices_include_vat": "*Prices include VAT.", "please_wait": "Please wait...", "adhoc_not_supported": "We're sorry, but this location does not currently support Ad Hoc.", "please_try_later": "Please try again later.", "payment_notice": "Payment Notice", "reserve_security_amount": "As a security measure, we <0>reserve</0> <1>{{amountToBlock}}</1> on your payment method.", "charge_cost": "After the charging process, only the actual <0>costs incurred</0> will be debited and the <1>remaining amount</1> will be <2>released</2>.", "plug_in_charging_cable": "Plug in charging cable now", "follow_signs": "Please follow signs / stickers at the charging point.", "proceed_to_payment": "Proceed to Payment", "payment_data": "Payment Data", "unknown_error": "An unexpected error occurred. Please try again.", "back_home": "Back to Homepage", "cancel_failed": "The start process could not be canceled. But don't worry, the amount blocked on the payment method will be automatically released after approximately 7 days.", "session_canceled": "Session Canceled", "cancel_successfully": "The start process has been successfully canceled and the reserved amount on the payment method will be released.", "start_session": "Start Session", "thanks_for_charging": "Thank you for charging", "start_session_again": "Start Session Again", "start_session_failed": "Failed to start session. The reserved amount on the payment method will be released.", "unknown_error_short": "Unknown Error", "home": "Home", "longer_than_usual": "Takes longer than usual...", "start_session_failed_sorry": "We're sorry, we couldn't start your session. We will refund your reserved amount shortly.", "connecting": "Conectando..., puede tardar hasta 60 segundos", "cable_was_plugged": "Was the charging cable plugged in?", "cable_already_plugged": "Charging cable already plugged in?", "acknowledge": "Acknowledge", "cancel_session": "Cancel Session", "apply": "Apply", "select_charger": "Select Charger", "start": "Start", "cancel": "Cancel", "change_charger": "Change Charger", "cancel_and_refund": "Do you really want to cancel the start process? <0></0> Once the process is canceled, the previously blocked amount on the payment method will be released.", "cannot_start_charger": "The selected charger cannot be started. Please choose another charger or cancel the process.", "scanned": "scanned", "first_disconnect_from_car": "Please first unlock and remove the cable from the car, then the charging point will release the cable.", "timeout_cancel": "Timeout! <0></0> Charging session could not be completed.", "disconnect_and_godbye": "Please end the charging session at the car and remove the cable from the car. <0></0>Then the charging point will release the cable. <0></0>Thank you and have a safe journey!", "interested_in_eulektro": "Interested in EULEKTRO? Want more information about offers, promotions, and developments?", "follow_us": "Then follow us:", "auth_payment": "Authorizing payment...", "accept": "Accept", "amount_will_be_blocked": "*The amount will be blocked on the card until the charging process is completed.", "qr_not_found": "We couldn't find a charging point for this QR code.", "chargepoint_not_active": "This may be due to the charging point not being active yet or there are connection issues to the backend.", "ok": "Ok", "cable_locked": "Cable locked?", "session_finished": "Charging session finished", "thanks_for_charging_endscreen": "<0>Thank you,</0><1>for charging with us.</1>", "cannot_remove_cable": "Cable stuck?", "help": "Help", "more_info": "More Info", "active_session_found": "Active charging session found", "view_session": "View", "start_another_session": "Start another session", "email_invoice_label": "Enter email address for <0></0> invoice", "email_placeholder": "Enter email address", "pls_valid_email": "Please enter a valid email address.", "save": "Save", "connection_attempt": "Connecting...", "charged": "Charged", "charging_time": "Charging Time", "request_invoice": "Request Invoice", "stop_charging": "Stop Charging", "info_invoice": "Invoice will be sent to {{invoiceMail}} after charging is complete.", "session_will_be_stopped": "Charging session will be stopped", "finishing_session": "Finishing charging session", "session_is_running": "Charging session is running", "thanks_for_payment": "Thank you for the payment", "memorize_mail": "Save Email", "blocking_fee": "Blocking Fee", "maximum": "Maximum", "minimum": "Minimum", "max": "Max", "min": "Min", "minute": "Minute", "beginning": "after"}