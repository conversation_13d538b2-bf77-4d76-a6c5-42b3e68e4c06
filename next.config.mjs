/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */
await import("./src/env.mjs");
import nextTranslate from 'next-translate-plugin';
/** @type {import("next").NextConfig} */
const config = {
  reactStrictMode: false,
  experimental: {
    serverComponentsExternalPackages: ['prisma'],
  },
  /**
   * If you have `experimental: { appDir: true }` set, then you must comment the below `i18n` config
   * out.
   *
   * @see https://github.com/vercel/next.js/issues/41980
   */

};
export default nextTranslate(config);
