{"name": "adhoc", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "postinstall": "prisma generate", "lint": "next lint", "start": "next start", "prisma_generate": "npx prisma format && npx prisma generate"}, "prisma": {"schema": "prisma/schema.prisma"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "5.15.0", "@react-hooks-library/core": "^0.6.2", "@stripe/react-stripe-js": "^2.1.0", "@stripe/stripe-js": "^1.54.0", "@t3-oss/env-nextjs": "^0.4.0", "@tailwindcss/forms": "^0.5.3", "archiver": "^7.0.1", "bson": "^5.2.0", "iconv-lite": "^0.6.3", "luxon": "^3.3.0", "next": "14.2.25", "next-auth": "^4.24.7", "next-https": "^1.1.10", "next-intl": "^3.15.5", "next-translate": "2.6.2", "next-translate-plugin": "2.6.2", "nodemailer": "^6.9.1", "pdfkit": "^0.13.0", "rate-limiter-flexible": "^5.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.43.9", "stripe": "^12.3.0", "zod": "^3.21.4"}, "devDependencies": {"@types/archiver": "^6.0.3", "@types/eslint": "^8.21.3", "@types/luxon": "^3.3.0", "@types/node": "^18.15.13", "@types/nodemailer": "^6.4.8", "@types/pdfkit": "0.12.9", "@types/prettier": "^2.7.3", "@types/react": "^18.0.38", "@types/react-dom": "^18.2.4", "@types/uuid": "^9.0.1", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "autoprefixer": "^10.4.1", "eslint": "^8.35.0", "eslint-config-next": "^13.4.9", "local-ssl-proxy": "^2.0.5", "postcss": "^8.4.24", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.2.8", "prisma": "5.15.0", "react-icons": "^5.2.1", "tailwindcss": "^3.3.2", "typescript": "^5.1.6", "uuid": "^9.0.0", "winston": "^3.8.2", "winston-daily-rotate-file": "^4.7.1", "zod-prisma-types": "^2.7.1"}}