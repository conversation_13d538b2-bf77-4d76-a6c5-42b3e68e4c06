generator client {
  provider = "prisma-client-js"
  output   = "./client"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL_MONGODB")
}

generator zod {
  provider                         = "zod-prisma-types"
  output                           = "./generated/zod"
  useMultipleFiles                 = false // default is false
  createInputTypes                 = false // default is true
  createModelTypes                 = true // default is true
  addInputTypeValidation           = true // default is true
  addIncludeType                   = true // default is true
  addSelectType                    = true // default is true
  validateWhereUniqueInput         = true // default is false
  createOptionalDefaultValuesTypes = true // default is false
  createRelationValuesTypes        = false // default is false
  createPartialTypes               = true // default is false
  useDefaultValidators             = false // default is true
  coerceDate                       = false // default is true
  writeNullishInModelTypes         = true // default is false
  prismaClientPath                 = "../../client" // default is client output path
}

model Cdr {
  id                      String               @id @map("_id")
  auth_method             String
  cdr_location            CdrCdrLocation
  cdr_token               CdrCdrToken
  charging_periods        CdrChargingPeriods[]
  country_code            String
  credit                  Boolean
  currency                String
  end_date_time           String
  last_updated            String
  party_id                String
  session_id              String
  start_date_time         String
  tariffs                 CdrTariffs[]
  total_cost              CdrTotalCost
  total_energy            Json
  total_energy_cost       CdrTotalEnergyCost
  total_fixed_cost        CdrTotalFixedCost
  total_parking_cost      CdrTotalParkingCost
  total_parking_time      Float
  total_time              Float
  total_time_cost         CdrTotalTimeCost
  authorization_reference String
  paymentIntent           PaymentIntent?
}

type CdrCdrLocation {
  id                   String
  address              String
  city                 String
  connector_format     String
  connector_id         String
  connector_power_type String
  connector_standard   String
  coordinates          CdrCdrLocationCoordinates
  country              String
  evse_id              String
  evse_uid             String
  name                 String
  postal_code          String
}

type CdrCdrLocationCoordinates {
  latitude  String
  longitude String
}

type CdrCdrToken {
  contract_id String
  type        String
  uid         String
}

type CdrChargingPeriods {
  dimensions      CdrChargingPeriodsDimensions[]
  start_date_time String
  tariff_id       String
}

type CdrChargingPeriodsDimensions {
  type   String
  volume Json
}

type CdrTariffs {
  country_code    String
  currency        String
  elements        CdrTariffsElements[]
  id              String
  last_updated    String?
  party_id        String
  tariff_alt_text CdrTariffsTariffAltText[]
  type            String
}

type CdrTariffsElements {
  price_components CdrTariffsElementsPriceComponents[]
}

type CdrTariffsElementsPriceComponents {
  /// Multiple data types found: Float: 50%, Int: 50% out of 4 sampled entries
  price     Json
  /// Multiple data types found: Int: 50%, BigInt: 50% out of 4 sampled entries
  step_size Json
  type      String
}

type CdrTariffsTariffAltText {
  language String
  text     String
}

type CdrTotalCost {
  excl_vat Float
}

type CdrTotalEnergyCost {
  excl_vat Float
}

type CdrTotalFixedCost {
  excl_vat Float
}

type CdrTotalParkingCost {
  excl_vat Float
}

type CdrTotalTimeCost {
  excl_vat Float
}

enum KindOfFile {
  Invoice
}

type InvoiceFiles {
  name             String
  application_type String
  path             String
  kind_of_file     KindOfFile?
  create_date      DateTime    @default(now())
}

type InvoiceHistory {
  date String
  text String
}

type InvoiceInvoicePositions {
  position         Int?
  title            String?
  description      String?
  amount           Float
  unit             String
  unit_price       Float?
  unit_price_gross Float?
  sum_gross        Float?
  sum_net          Float?
  sum_tax          Float?
  tax_rate         Float
}

type SessionCdrToken {
  contract_id String
  type        String
  uid         String
}

type SessionChargingPeriods {
  dimensions      SessionChargingPeriodsDimensions[]
  start_date_time String
  tariff_id       String                             @default("")
}

type SessionChargingPeriodsDimensions {
  type   String
  volume Float
}

type SessionTotalCost {
  excl_vat Float
}

model Handshake {
  id        String @id @default(auto()) @map("_id") @db.ObjectId
  cpo_token String
  emp_token String
  url       String
}

enum InvoiceStatusEnum {
  DRAFT // invoice was created but no invoice number
  INMUTABLE_WRITTEN // invoice with invoice number and pdf was created
  PAID // final invoice was paid
}

model Invoice {
  id                      String                    @id @default(auto()) @map("_id") @db.ObjectId
  emp_country_code        String?
  emp_party_id            String?
  currency                String?
  files                   InvoiceFiles[]
  history                 InvoiceHistory[]
  invoicePositions        InvoiceInvoicePositions[]
  invoice_date            DateTime
  invoice_number          String?
  kind_of_invoice         String?
  status                  InvoiceStatusEnum
  metadata                Json?
  paid_date               DateTime?
  sentAsEmail             Boolean
  mailToSend              String?
  service_period_from     DateTime?
  service_period_to       DateTime?
  local_start_datetime    String?
  local_end_datetime      String?
  state_of_invoice        String?
  subject                 String?
  sum_gross               Float?
  sum_net                 Float?
  sum_tax                 Float?
  text                    String?
  create_date             DateTime                  @default(now())
  last_update             DateTime                  @updatedAt
  authorization_reference String
}

model InvoiceBatchMagicLink {
  id           String   @id @map("_id") @default(cuid())
  email        String
  tokenHash    String
  expiresAt    DateTime
  createdAt    DateTime @default(now())
  lastUsedAt   DateTime?
  usedCount    Int      @default(0)
  requesterIp  String?
  userAgent    String?
  purpose      String   @default("invoice_batch")

  @@index([email])
  @@index([expiresAt])
}



type LocationCoordinates {
  latitude  String
  longitude String
}

type LocationEvses {
  capabilities       String[]
  connectors         LocationEvsesConnectors[]
  coordinates        LocationEvsesCoordinates
  evse_id            String?
  last_updated       String
  physical_reference String?
  status             String
  uid                String
}

type LocationEvsesConnectors {
  id                 String
  standard           String
  format             String
  power_type         String
  max_voltage        Float
  max_amperage       Float
  max_electric_power Float?
  last_updated       String
  tariff_ids         String[]
}

type LocationEvsesCoordinates {
  latitude  String
  longitude String
}

type LocationOperator {
  name String
}

type LocationOwner {
  name String
}

model Location {
  id                   String              @id @map("_id")
  address              String
  charging_when_closed Boolean
  city                 String
  coordinates          LocationCoordinates
  country              String
  country_code         String
  evses                LocationEvses[]
  last_updated         String
  name                 String
  operator             LocationOperator?
  suboperator          LocationOperator?
  owner                LocationOwner?
  parking_type         String?
  party_id             String
  postal_code          String
  publish              Boolean
  /// Could not determine type: the field only had null or empty values in the sample set.
  publish_allowed_to   Json?
  time_zone            String
  paymentIntent        PaymentIntent?
  LocationPrice        LocationPrice[]
}

enum StatusEnum {
  CREATED // PaymentIntent wurde erstellt
  PAYMENT_GUARANTED // Zahlung wurde garantiert
  START_REQUESTED // Start wurde per OCPI als SESSION_START angefragt
  START_ACCEPTED // Start wurde per callback vom CPO akzeptiert
  START_REJECTED // Start wurde per callback vom CPO abgelehnt
  SESSION_RECEIVED // Session wurde per PUSH von CPO empfangen
  STOP_SESSION_REQUESTED // Stop wurde per OCPI als SESSION_STOP angefragt
  STOP_SESSION_ACCEPTED // Stop wurde per callback vom CPO akzeptiert
  STOP_SESSION_REJECTED // Stop wurde per callback vom CPO abgelehnt
  CDR_RECEIVED // CDR wurde per PUSH von CPO empfangen
  CAPTURED // Zahlung wurde eingezogen
  CAPTURE_FAILED // Zahlung konnte nicht eingezogen werden
  REFUNDED // Zahlung wurde zurückgebucht bzw wieder freigegeben
  REFUND_FAILED // Zahlung konnte nicht zurückgebucht werden
  INVOICE_SENT // Rechnung wurde per Mail an den Kunden versendet
}

model PaymentIntent {
  id                      String     @id @map("_id")
  amount                  Int
  amount_capturable       Int
  amount_received         Int
  session_start_token_uid String
  status                  StatusEnum @default(CREATED)
  authorization_reference String     @unique
  createdAt               DateTime   @default(now())
  updatedAt               DateTime   @updatedAt
  invoiceMail             String?
  session                 Session?   @relation(fields: [sessionId], references: [id])
  sessionId               String?    @unique
  cdr                     Cdr?       @relation(fields: [cdrId], references: [id])
  cdrId                   String?    @unique
  location                Location?  @relation(fields: [locationId], references: [id])
  locationId              String?    @unique
  evseId                  String     @default("Unknown")
  tax_rate                Float
  energy_price            Float
  blocking_fee            Float
  blocking_fee_start      Int
  blocking_fee_max        Float
  session_fee             Float
  min_kwh_in_kwh          Float      @default(0)
  min_time_in_min         Int        @default(0)
}

model Session {
  id                      String                   @id @map("_id")
  auth_method             String
  cdr_token               SessionCdrToken
  charging_periods        SessionChargingPeriods[]
  connector_id            String
  country_code            String
  currency                String
  end_date_time           String?
  evse_uid                String
  kwh                     Float
  last_updated            String
  location_id             String
  party_id                String
  start_date_time         String
  status                  String
  total_cost              SessionTotalCost
  authorization_reference String
  paymentIntent           PaymentIntent?
}

model Counter {
  id             String @id @map("_id")
  sequence_value Int
}

model EmpAddress {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  from         DateTime
  to           DateTime
  street       String
  house_number String
  postal_code  String
  city         String
  country      String
  ust_id       String
  vat_id       String
  Emp          Emp?     @relation(fields: [empId], references: [id])
  empId        String?  @db.ObjectId
}

model Emp {
  id              String           @id @default(auto()) @map("_id") @db.ObjectId
  name            String
  country_code    String?
  party_id        String?
  amount_to_block Float            @default(5000)
  min_kwh_in_kwh  Float            @default(0)
  min_time_in_min Int              @default(0)
  address         EmpAddress[]
  price           EmpPrice[]
  location_price  LocationPrice[]
  ocpi_connection OcpiConnection[]

  @@unique([country_code, party_id])
}

model OcpiConnection {
  id           String  @id @default(auto()) @map("_id") @db.ObjectId
  name         String
  url          String
  inital_token String?
  emp_secret   String?
  cpo_secret   String?
  version      String?
  Emp          Emp?    @relation(fields: [empId], references: [id])
  empId        String? @db.ObjectId
}

enum CurrentType {
  AC
  DC
}

model EmpPrice {
  id                 String      @id @default(auto()) @map("_id") @db.ObjectId
  start              DateTime
  end                DateTime
  energy_price       Float
  blocking_fee       Float
  blocking_fee_start Int
  blocking_fee_max   Float
  session_fee        Float
  tax_rate           Float
  Emp                Emp?        @relation(fields: [empId], references: [id])
  empId              String?     @db.ObjectId
  current_type       CurrentType @default(AC)
}

model LocationPrice {
  id                 String      @id @default(auto()) @map("_id") @db.ObjectId
  location           Location?   @relation(fields: [locationId], references: [id])
  locationId         String?
  start              DateTime
  end                DateTime
  energy_price       Float
  blocking_fee       Float
  blocking_fee_start Int
  blocking_fee_max   Float
  session_fee        Float
  tax_rate           Float
  Emp                Emp?        @relation(fields: [empId], references: [id])
  empId              String?     @db.ObjectId
  current_type       CurrentType @default(AC)
}
