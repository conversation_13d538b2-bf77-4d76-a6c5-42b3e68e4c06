import { type NextRequest, NextResponse } from "next/server";
import { StatusEnum } from "../../../../../../prisma/client";
import { z } from "zod";
import { env } from "~/env.mjs";
import { CpoHeaders } from "~/utils/cpo/header";
import { prisma } from "~/server/db";
import { SendErrorMessage, SendInfoMessage } from "~/utils/chat/sendMessage";
import { stripe } from "~/utils/stripe/stripe";
import { generateEULToken } from "~/utils/ocpiAuth/token";
import { getEmpByEvseId, getPriceForStationByEvseId } from "~/utils/data/getBy";

export const revalidate = 0;

const StartSessionPayloadSchema = z.object({
  paymentIntentId: z.string(),
  evseId: z.string(),
});

const startSession = async (evseId: string) => {
  const url = `${env.LONGSHIP_DOMAIN}/ocpi/2.2/commands/START_SESSION`;

  const sessionStartTokenUid = generateEULToken(20);
  const authorizationReference = generateEULToken(20);

  const location = await prisma.location.findFirst({
    where: {
      evses: {
        some: {
          evse_id: evseId,
        },
      },
    },
  });

  if (location === null) {
    void (await SendInfoMessage(
      `location not found to start session on evse: ${evseId}`
    ));
    throw new Error("location not found");
  }

  const evse = location.evses.find((evse) => evse.evse_id === evseId);

  if (!evse) {
    void (await SendErrorMessage(`${evseId} not found in location object`));
  }
  const connectorId = evse?.connectors?.[0]?.id || 0;

  const payload = {
    response_url: `${env.OCPI_DOMAIN}/api/longship/command/response/startSession/${authorizationReference}`,
    location_id: location.id,
    evse_uid: evse?.uid,
    connector_id: connectorId,
    authorization_reference: authorizationReference,
    token: {
      uid: sessionStartTokenUid,
      type: "RFID",
      auth_id: "ABC12", // ToDo löschen?
      issuer: "Eulektro GmbH",
      whitelist: "NEVER",
      last_updated: "2023-04-22T20:33:34Z", // ToDo generieren ?!
      party_id: "EUL", //ToDO aus EMP lesen ?
      country_code: "DE", //ToDO aus EMP lesen ?
      contract_id: "n/a",
      valid: true,
    },
  };
  await SendInfoMessage(
    `Trying to start session on evse: ${evseId}\nauthref:${authorizationReference}`
  );
  const response = await fetch(url, {
    method: "POST",
    headers: CpoHeaders,
    body: JSON.stringify(payload),
  });
  if (response.status !== 200) {
    const responseText = await response.text();
    await SendInfoMessage(
      `start session failed on evse: ${evseId} and authref=${authorizationReference} with status: ${response.status} - Statustext: ${responseText}`
    );
    /*throw new Error(
      `Exeption Message: start session failed  ${evseId} and authref=${authorizationReference} with status: ${response.status}, ${responseText}`
    );*/
  }

  return {
    status: response.status,
    authorizationReference: authorizationReference,
    sessionStartTokenUid: sessionStartTokenUid,
  };
};

export async function POST(request: NextRequest) {
  const payload = StartSessionPayloadSchema.safeParse(await request.json());

  try {
    if (payload.success && payload.data.paymentIntentId) {
      const { price, current_type } = await getPriceForStationByEvseId(
        payload.data.evseId
      );

      if (!price) {
        void (await SendInfoMessage(
          `cant find price for station: ${payload.data.evseId}`
        ));
        return NextResponse.json("error", { status: 400 });
      }

      const stripePaymentIntent = await stripe.paymentIntents.retrieve(
        payload.data.paymentIntentId
      );
      const emp = await getEmpByEvseId(payload.data.evseId);

      if (!emp) {
        void (await SendInfoMessage(
          `EMP not found by evseid= ${
            payload?.data?.evseId ?? "evseid was null"
          }`
        ));
      }
      if (stripePaymentIntent.amount_capturable >= emp.amount_to_block) {
        // wir können geld einziehen
        const paymentIntent = await prisma.paymentIntent.findUnique({
          where: {
            id: payload.data.paymentIntentId,
          },
        });
        if (paymentIntent) {
          // ToDo cancel paymentIntent?
          void (await SendInfoMessage(
            `paymentIntent already exists ${paymentIntent.id}`
          ));
          return NextResponse.json("error");
        }

        try {
          // ToDo check status des Ladepunktes. Nur wenn der Ladepunkt den Status available oder preparing hat, kann die Session gestartet werden
          const location = await prisma.location.findFirst({
            where: {
              evses: {
                some: {
                  evse_id: payload.data.evseId,
                },
              },
            },
          });
          if (!location) {
            void (await SendInfoMessage(
              `location not found to start session on evse: ${payload.data.evseId}`
            ));
            return NextResponse.json("error");
          }
          const evse = location.evses.find(
            (evse) => evse.evse_id === payload.data.evseId
          );
          const evse_uid = evse?.uid;
          if (!evse_uid) {
            void (await SendInfoMessage(
              `evse not found to start session on evse: ${payload.data.evseId}`
            ));
            return NextResponse.json("error");
          }

          const ValidateLocationResponse = z
            .object({
              data: z
                .object({
                  uid: z.string(),
                  evse_id: z.string(),
                  status: z.string(),
                })
                .nonstrict(),
            })
            .nonstrict();

          const evseData = await fetch(
            `${env.LONGSHIP_DOMAIN}/ocpi/2.2/locations/${location.id}/${evse_uid}`,
            {
              method: "GET",
              headers: CpoHeaders,
            }
          );
          const evseDataJson: unknown = await evseData.json();
          const validateEvseData =
            ValidateLocationResponse.safeParse(evseDataJson);
          if (!validateEvseData.success) {
            void (await SendInfoMessage("evse_data_json not success"));
            return NextResponse.json("error");
          }

          if (
            !(
              validateEvseData.data.data.status == "AVAILABLE" ||
              validateEvseData.data.data.status == "PREPARING" ||
              validateEvseData.data.data.status == "PLANNED"
            )
          ) {
            void (await SendInfoMessage(
              `cant start session for ${payload.data.evseId} with location status ${validateEvseData.data.data.status}`
            ));
            return NextResponse.json("error");
          }

          const { status, authorizationReference, sessionStartTokenUid } =
            await startSession(payload.data.evseId);

          if (status != 200) {
            await SendInfoMessage(
              `SessionStart returned ${status} creating paymentIntent in Mongo as workaround`
            );
          }

          if (authorizationReference && sessionStartTokenUid) {
            const createdIntent = await prisma.paymentIntent.create({
              data: {
                id: stripePaymentIntent.id,
                amount: stripePaymentIntent.amount,
                amount_capturable: stripePaymentIntent.amount_capturable,
                amount_received: stripePaymentIntent.amount_received,
                authorization_reference: authorizationReference,
                session_start_token_uid: sessionStartTokenUid,
                status: StatusEnum.START_REQUESTED,
                evseId: payload.data.evseId,
                energy_price: price.energy_price,
                session_fee: price.session_fee,
                blocking_fee: price.blocking_fee,
                blocking_fee_max: price.blocking_fee_max,
                blocking_fee_start: price.blocking_fee_start,
                tax_rate: price.tax_rate,
                min_kwh_in_kwh: emp.min_kwh_in_kwh,
                min_time_in_min: emp.min_time_in_min,
              },
            });
            return NextResponse.json({
              authorization_reference: authorizationReference,
            });
          }
        } catch (error) {
          void (await SendErrorMessage(
            `Catch block /command/startSession ${payload.data.evseId} ${
              payload.data.paymentIntentId
            } ${(error as Error).message ?? "error.message is empty"}`
          ));
          await stripe.paymentIntents.update(payload.data.paymentIntentId, {
            metadata: {
              workaround: payload.data.evseId,
            },
          });
          /*
          await stripe.paymentIntents.cancel(payload.data.paymentIntentId);
          void (await SendInfoMessage(
            `start session failed on evse: ${payload.data.evseId} and the paymentIntent was canceled`
          ));*/
          return NextResponse.json({
            error:
              "start session failed (workaround in place not to cancel payment intent)",
            status: StatusEnum.REFUNDED,
          });
        }
      } else {
        await stripe.paymentIntents.cancel(payload.data.paymentIntentId);
        void (await SendInfoMessage(
          `start session failed on evse: ${payload.data.evseId} and the paymentIntent was canceled. The amount_to_block was to low`
        ));
      }
    }
  } catch (error) {
    return NextResponse.json("error");
  }

  return NextResponse.json("error");
}
