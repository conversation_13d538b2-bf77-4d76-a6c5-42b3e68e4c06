POST https://beta.adhoc.eulektro.de/api/adhoc/command/startSession
content-type: application/json

{
  "clientSecret": "pi_3N5UyJINwl1ZJuJd1oa8w4wC",
  "evseId": "DE*EUL*E000X*01"
}


###
GET https://beta.ocpi.longship.io/ocpi/2.2/locations/DEEULS000X/
Authorization: Token 7d5bfda3b8cd4a24874575b9ce198d44

###

###
GET https://beta.ocpi.longship.io/ocpi/2.2/locations/DEEULS000X/b350f509-762f-4288-b4a7-5a2247f63d71
Authorization: Token 7d5bfda3b8cd4a24874575b9ce198d44


###

POST https://test.adhoc.eulektro.de/api/longship/command/response/startSession/1170d4d64c845ce7f314
authorization: Token ffff05f2c47d60bdac8f4f2b4dde0bbc
content-type: application/json

{
"status": "Accepted"
}
