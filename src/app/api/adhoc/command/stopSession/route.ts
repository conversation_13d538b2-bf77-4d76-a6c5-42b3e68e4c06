import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { env } from "~/env.mjs";
import { CpoHeaders } from "~/utils/cpo/header";
import { prisma } from "~/server/db";
import { SendInfoMessage } from "~/utils/chat/sendMessage";
import { fetchSessionFromApiBackend } from "~/utils/data/getBy";

export const revalidate = 0;

const StopSessionPayloadSchema = z.object({
  paymentIntentId: z.string(),
  evseId: z.string(),
});

const sessionSchema = z.object({
  id: z.string(),
  country_code: z.string(),
  party_id: z.string(),
  start_date_time: z.string(),
  end_date_time: z.string().nullish(),
  kwh: z.number(),
  cdr_token: z.object({
    uid: z.string(),
    type: z.string(),
    contract_id: z.string(),
  }),
  auth_method: z.string(),
  location_id: z.string(),
  evse_uid: z.string(),
  connector_id: z.string(),
  currency: z.string(),
  charging_periods: z.array(
    z.object({
      start_date_time: z.string(),
      tariff_id: z.string().optional().default(""),
      dimensions: z.array(
        z.object({
          type: z.string(),
          volume: z.number(),
        })
      ),
    })
  ),
  total_cost: z.object({
    excl_vat: z.number(),
  }),
  status: z.string(),
  last_updated: z.string(),
  authorization_reference: z.string(),
});

const OCPIResponseSchema = z.object({
  data: z.array(sessionSchema),
  status_code: z.number(),
  status_message: z.string(),
  timestamp: z.string(),
});

const findSessionIdByTokenUid = async (
  url: string,
  tokenUid: string,
  offset: number
): Promise<z.infer<typeof sessionSchema>> => {
  const response = await fetch(`${url}&offset=${offset}`, {
    headers: CpoHeaders,
  });

  const sessionArray = OCPIResponseSchema.safeParse(await response.json());
  if (sessionArray.success === false) {
    throw new Error("Invalid response");
  }
  const session = sessionArray.data.data.find(
    (session) => session.cdr_token.uid === tokenUid
  );

  if (session) {
    return session;
  } else {
    if (
      !response.headers.get("X-Total-Count") ||
      !response.headers.get("X-Limit")
    ) {
      throw new Error("No X-Total-Count or X-Limit header found");
    }
    const totalCount = parseInt(
      response.headers.get("X-Total-Count") || "50",
      10
    );
    const limit = parseInt(response.headers.get("X-Limit") || "50", 10);
    const currentOffset = offset;

    if (currentOffset + limit < totalCount) {
      return findSessionIdByTokenUid(url, tokenUid, currentOffset + limit);
    }
  }
  throw new Error("No session found");
};

export async function POST(request: NextRequest) {
  const payload = StopSessionPayloadSchema.safeParse(await request.json());

  if (!payload.success) {
    void (await SendInfoMessage("Stop Session PayloadSchema validation Error", {
      zodError: payload.error,
    }));
    return NextResponse.json({
      status_code: 3000,
      status_message: "Missing paymentIntendId",
      timestamp: new Date().toISOString(),
    });
  }

  try {
    const paymentIntent = await prisma.paymentIntent.findUnique({
      where: {
        id: payload.data.paymentIntentId,
      },
    });

    if (!paymentIntent) {
      throw new Error("No paymentIntent found");
    }

    const session = await prisma.session.findFirst({
      where: {
        cdr_token: {
          is: {
            uid: paymentIntent.session_start_token_uid.toUpperCase(),
          },
        },
      },
    });
    let sessionId = "";
    if (!session) {
      // Get all sessions from the last 24h and search for the id == session_id.data
      const currentDate = new Date();
      const dateFrom = new Date(currentDate);
      currentDate.setDate(currentDate.getDate() + 1);
      dateFrom.setDate(dateFrom.getDate() - 1);
      const dateTo = currentDate;
      const sessionUrl = `${
        env.LONGSHIP_DOMAIN
      }/ocpi/2.2/sessions?date_from=${dateFrom.toISOString()}&date_to=${dateTo.toISOString()}`;

      const sessionFromLongship = await findSessionIdByTokenUid(
        sessionUrl,
        paymentIntent.session_start_token_uid.toUpperCase(),
        0
      );
      //OCPI workaround via API
      if (!sessionFromLongship) {
        const sessionViaApi = await fetchSessionFromApiBackend(
          "",
          paymentIntent.authorization_reference
        );
        if (sessionViaApi) {
          sessionId = sessionViaApi.id;
        }
      } else {
        sessionId = sessionFromLongship.id;
      }
    } else {
      sessionId = session.id;
    }

    if (!session) {
      void (await SendInfoMessage(
        `No session found to STOP_SESSION for paymentIntent ${paymentIntent.id} on evse ${paymentIntent.evseId}`
      ));
      throw new Error("No session found in DB or ocpi or api");
    }
    void (await fetch(`${env.LONGSHIP_DOMAIN}/ocpi/2.2/commands/STOP_SESSION`, {
      headers: CpoHeaders,
      method: "POST",
      body: JSON.stringify({
        response_url: `${env.OCPI_DOMAIN}/api/longship/response/stopSession/${paymentIntent.id}`,
        session_id: `${sessionId}`,
      }),
    }));
  } catch (e) {
    return NextResponse.json("Error");
  }

  return NextResponse.json("ok");
}
