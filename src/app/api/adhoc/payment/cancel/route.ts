import { type NextRequest } from "next/server";
import { z } from "zod";

import { prisma } from "~/server/db";

import { SendInfoMessage } from "~/utils/chat/sendMessage";
import { stripe } from "~/utils/stripe/stripe";
import { ErrorResponse, SuccessResponse } from "~/utils/response/response";
import Stripe from "stripe";

export const revalidate = 0;

const PostPayloadSchema = z.object({
  paymentIntentId: z.string(),
});

export const POST = async (request: NextRequest) => {
  const payload = PostPayloadSchema.safeParse(await request.json());

  if (!payload.success) {
    void (await SendInfoMessage(
      "api/adhoc/payment/cancel Post Payload Schema failed"
    ));
    return ErrorResponse("Post Payload Schema failed");
  }

  const paymentIntent = await prisma.paymentIntent.findUnique({
    where: {
      id: payload.data.paymentIntentId,
    },
  });

  if (!paymentIntent) {
    // Super, dann wurde keine session gestartet und wir können refunden
    try {
      void (await stripe.paymentIntents.cancel(payload.data.paymentIntentId, {
        cancellation_reason: "requested_by_customer",
      }));
      // wenn erfolgreich, dann return succuess sonst return error
      return SuccessResponse("cancel was successful");
    } catch (e) {
      if (e instanceof Stripe.errors.StripeInvalidRequestError) {
        return ErrorResponse(
          `exception StripeInvalidRequestError cancel payment failed`,
          { status: e.statusCode }
        );
      }
      return ErrorResponse("exception cancel payment failed");
    }
  }
  return ErrorResponse("cancel payment failed");
};
