import { type NextRequest, NextResponse } from "next/server";
import { prisma } from "~/server/db";

import { stripe } from "~/utils/stripe/stripe";
import { InvoiceManager } from "~/utils/invoice";

import { Cdr, Invoice } from "../../../../../../prisma/client";

export const revalidate = 0;

export async function GET(request: NextRequest) {
  const token = request.nextUrl.searchParams.get("token");

  if (token !== "490327urzh0iqwg3f32mf03q2ne") {
    return NextResponse.json("error", { status: 500 });
  }
  const invoiceArray = await prisma.invoice.findRaw({
    filter: {
      "metadata.paymentIntent": "pi_3QXmvAINwl1ZJuJd0SfMvFse",
    },
  });

  if (Array.isArray(invoiceArray) && invoiceArray.length > 0) {
    const invoiceObj = invoiceArray[0] as unknown as {
      _id: { $oid: string };
    };
    const invoice = (await prisma.invoice.findUnique({
      where: { id: invoiceObj._id.$oid },
    })) as Invoice;

    try {
      const invoiceManager = new InvoiceManager();
      await invoiceManager.finalize(invoice.id);
      await invoiceManager.setStatusToPaid(invoice.id);
      //await invoiceManager.send(invoice.id, "pi_3Q2SB9INwl1ZJuJd1YXGDEXp");

      return NextResponse.json("ok");
    } catch (e) {
      return NextResponse.json("error");
    }
  }
}
