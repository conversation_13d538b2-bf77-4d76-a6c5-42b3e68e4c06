import { type NextRequest, NextResponse } from "next/server";
import { prisma } from "~/server/db";

import { stripe } from "~/utils/stripe/stripe";
import { InvoiceManager } from "~/utils/invoice";

import { Cdr, Invoice } from "../../../../../../prisma/client";

export const revalidate = 0;

export async function GET(request: NextRequest) {
  const token = request.nextUrl.searchParams.get("token");
  const cdrId = request.nextUrl.searchParams.get("cdrId") ?? "";
  const paymentIntentId =
    request.nextUrl.searchParams.get("paymentIntentId") ?? "";

  if (token !== "490327urzh0iqwg3f32mf03q2ne" || !cdrId || !paymentIntentId) {
    return NextResponse.json("error", { status: 500 });
  }
  const invoiceArray = await prisma.invoice.findRaw({
    filter: {
      "metadata.paymentIntent": paymentIntentId,
    },
  });

  if (Array.isArray(invoiceArray) && invoiceArray.length > 0) {
    const invoiceObj = invoiceArray[0] as unknown as {
      _id: { $oid: string };
    };
    const invoice = (await prisma.invoice.findUnique({
      where: { id: invoiceObj._id.$oid },
    })) as Invoice;
    const cdr = (await prisma.cdr.findUnique({
      where: { id: cdrId ?? "" },
    })) as Cdr;
    let amount_to_capture_in_cent = 0;
    if (invoice.sum_gross) {
      amount_to_capture_in_cent = Math.round(invoice.sum_gross * 100);
    }

    try {
      if (
        cdr.total_energy &&
        cdr.id &&
        cdr.start_date_time &&
        cdr.end_date_time &&
        cdr.cdr_location.evse_id &&
        cdr.cdr_location.address
      ) {
        const res = await stripe.paymentIntents.capture(paymentIntentId, {
          amount_to_capture: amount_to_capture_in_cent,
          metadata: {
            cdr_id: cdr.id,
            start: cdr.start_date_time as unknown as string,
            end: cdr.end_date_time as unknown as string,
            total_energy: cdr.total_energy as number,
            evse: cdr.cdr_location.evse_id,
            address: cdr.cdr_location.address,
            eulektroInvoice: invoice.id,
          },
          statement_descriptor_suffix: `${cdr.id.slice(-11)}X`,
        });
      }

      const invoiceManager = new InvoiceManager();
      await invoiceManager.finalize(invoice.id);
      await invoiceManager.setStatusToPaid(invoice.id);
      await invoiceManager.send(invoice.id, paymentIntentId);

      return NextResponse.json("ok");
    } catch (e) {
      return NextResponse.json("error");
    }
  }
}
