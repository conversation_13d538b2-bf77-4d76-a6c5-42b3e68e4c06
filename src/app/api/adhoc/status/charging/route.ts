import { type NextRequest, NextResponse } from "next/server";

import { z } from "zod";
import { prisma } from "~/server/db";
import { fetchSessionFromApiBackend } from "~/utils/data/getBy";

export const revalidate = 0;

const StartSessionPayloadSchema = z.object({
  paymentIntentId: z.string(),
});

export async function POST(request: NextRequest) {
  const payload = StartSessionPayloadSchema.safeParse(await request.json());

  if (!payload.success) {
    return NextResponse.json({
      data: {
        kwh: 0,
        duration: "00:00:00",
        status: "unknown",
      },
      status_code: 3000,
      status_message: "Payload not expected",
      timestamp: new Date().toISOString(),
    });
  }

  const paymentIntent = await prisma.paymentIntent.findUnique({
    where: {
      id: payload.data.paymentIntentId,
    },
  });

  if (!paymentIntent) {
    return NextResponse.json({
      data: {
        kwh: 0,
        duration: "00:00:00",
        status: "unknown",
      },
      status_code: 3000,
      status_message: "PaymentIntent not found",
      timestamp: new Date().toISOString(),
    });
  }

  const session = await prisma.session.findFirst({
    where: {
      cdr_token: {
        is: {
          uid: paymentIntent.session_start_token_uid.toUpperCase(),
        },
      },
    },
  });

  let formattedDuration = "00:00:00";
  let kWh = 0.0;

  //wenn keine EVSE UID dann kam die session über den workaround
  //somit gibt es keine aktuellen daten und es muss die API genommen werden
  if (session && session.evse_uid) {
    // Berechnen Sie die Dauer zwischen start_time und dem aktuellen Zeitpunkt
    const startTimeDate = new Date(session.start_date_time);
    const currentTime = new Date();
    const durationMs = currentTime.valueOf() - startTimeDate.valueOf();

    // Konvertieren Sie die Dauer in Stunden, Minuten und Sekunden
    const seconds = Math.floor((durationMs / 1000) % 60);
    const minutes = Math.floor((durationMs / (1000 * 60)) % 60);
    const hours = Math.floor(durationMs / (1000 * 60 * 60));

    // Formatieren Sie die Dauer im gewünschten Format (hh:mm:ss)
    formattedDuration = `${String(hours).padStart(2, "0")}:${String(
      minutes
    ).padStart(2, "0")}:${String(seconds).padStart(2, "0")}`;
    kWh = session.kwh;
  } else if (session && !session.evse_uid) {
    const sessionViaApi = await fetchSessionFromApiBackend(
      "",
      paymentIntent.authorization_reference
    );
    if (sessionViaApi) {
      kWh = sessionViaApi.totalEnergyInKwh;
      const startTimeDate = new Date(sessionViaApi.created);
      const currentTime = new Date();
      const durationMs = currentTime.valueOf() - startTimeDate.valueOf();

      // Konvertieren Sie die Dauer in Stunden, Minuten und Sekunden
      const seconds = Math.floor((durationMs / 1000) % 60);
      const minutes = Math.floor((durationMs / (1000 * 60)) % 60);
      const hours = Math.floor(durationMs / (1000 * 60 * 60));

      // Formatieren Sie die Dauer im gewünschten Format (hh:mm:ss)
      formattedDuration = `${String(hours).padStart(2, "0")}:${String(
        minutes
      ).padStart(2, "0")}:${String(seconds).padStart(2, "0")}`;
    }
  }

  return NextResponse.json({
    data: {
      kwh: kWh,
      duration: formattedDuration,
      status: paymentIntent.status,
    },
    status_code: 10000,
    status_message: "Success",
    timestamp: new Date().toISOString(),
  });
}
