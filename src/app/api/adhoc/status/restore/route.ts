import { type NextRequest, NextResponse } from "next/server";

import { z } from "zod";
import { prisma } from "~/server/db";
import { StatusEnum } from "../../../../../../prisma/client";
import { fetchSessionFromBackend } from "~/utils/data/getBy";
import { env } from "~/env.mjs";

export const revalidate = 0;

const RestorePayloadSchema = z.object({
  paymentIntentId: z.string(),
});

export async function POST(request: NextRequest) {
  const payload = RestorePayloadSchema.safeParse(await request.json());

  if (!payload.success) {
    return NextResponse.json({
      status_code: 3000,
      status_message: "Payload not expected",
      timestamp: new Date().toISOString(),
    });
  }

  const paymentIntentFromDB = await prisma.paymentIntent.findFirst({
    where: {
      id: payload.data.paymentIntentId,
    },
  });

  if (
    paymentIntentFromDB &&
    (paymentIntentFromDB.status === StatusEnum.START_ACCEPTED ||
      paymentIntentFromDB.status === StatusEnum.START_REQUESTED)
  ) {
    const currentDate = new Date();
    const dateFrom = new Date(currentDate);
    currentDate.setDate(currentDate.getDate() + 1);
    dateFrom.setDate(dateFrom.getDate() - 1);
    const sessionUrl = `${
      env.LONGSHIP_DOMAIN
    }/ocpi/2.2/sessions?date_from=${dateFrom.toISOString()}&date_to=${currentDate.toISOString()}`;
    const sessionFromBackend = await fetchSessionFromBackend(
      sessionUrl,
      "",
      paymentIntentFromDB.authorization_reference
    );

    if (sessionFromBackend && sessionFromBackend.status === "ACTIVE")
      return NextResponse.json({
        status_code: 200,
        data: {
          id: paymentIntentFromDB.id,
          createdAt: paymentIntentFromDB.createdAt,
          charger: paymentIntentFromDB.evseId,
        },
        status_message:
          "PaymentIntent state still active, view can be restored (liveStatus used)",
        timestamp: new Date().toISOString(),
      });
  }

  if (
    paymentIntentFromDB &&
    paymentIntentFromDB.status === StatusEnum.SESSION_RECEIVED
  ) {
    return NextResponse.json({
      status_code: 200,
      data: {
        id: paymentIntentFromDB.id,
        createdAt: paymentIntentFromDB.createdAt,
        charger: paymentIntentFromDB.evseId,
      },
      status_message: "PaymentIntent state still active, view can be restored",
      timestamp: new Date().toISOString(),
    });
  } else {
    return NextResponse.json({
      status_code: 3000,
      data: { activeIds: null },
      status_message: "PaymentIntent not active anymore restore not possible",
      timestamp: new Date().toISOString(),
    });
  }
}
