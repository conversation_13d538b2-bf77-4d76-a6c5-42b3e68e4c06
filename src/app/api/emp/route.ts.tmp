import { type NextRequest, NextResponse } from "next/server";
import { prisma } from "~/server/db";
export const revalidate = 0;
export const GET = async (_request: NextRequest) => {
  const res = await prisma.emp.findFirst({
    where: {
      name: "Eulektro GmbH",
    },
  });
  if (res) {
    return NextResponse.json("emp already exists");
  }

  void (await prisma.emp.create({
    data: {
      name: "Eulektro GmbH",
      country_code: "DE",
      party_id: "EUL",
      amount_to_block: 200,
      ocpi_connection: {
        create: {
          name: "Longship",
          url: "https://beta.adhoc.eulektro.de/ocpi/versions",
          inital_token: "",
          emp_secret: "",
          cpo_secret: "",
          version: "2.2.1",
        },
      },
      address: {
        create: {
          from: "2021-01-01T00:00:00.000Z",
          to: "2021-12-31T23:59:59.999Z",
          street: "Werderstraße",
          house_number: "69",
          postal_code: "28199",
          city: "Bremen",
          country: "Deutschland",
          ust_id: "DE123456789",
          vat_id: "DE123456789",
        },
      },
      price: {
        create: {
          tax_rate: 19,
          start: "2022-01-01T00:00:00.000Z",
          end: "2050-12-31T23:59:59.999Z",
          energy_price: 0.49,
          parking_price: 0,
          session_fee: 1,
        },
      },
      location_price: {
        create: {
          tax_rate: 19,
          start: "2022-01-01T00:00:00.000Z",
          end: "2050-12-31T23:59:59.999Z",
          energy_price: 0.45,
          parking_price: 0,
          session_fee: 1,
          locationId: "DEEULS0001",
        },
      },
    },
    include: {
      address: true,
      price: true,
      location_price: {
        include: {
          location: true,
        },
      },
      ocpi_connection: true,
    },
  }));
  return NextResponse.json("ok");
};
