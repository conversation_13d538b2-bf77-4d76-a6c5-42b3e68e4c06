// app/api/invoice/listByToken/route.ts
import { NextResponse, type NextRequest } from "next/server";
import { prisma } from "~/server/db";
import { z } from "zod";
import { sha256 } from "~/utils/security/magicLink";
import { cookies } from "next/headers";

export const revalidate = 0;

const Schema = z.object({
  id: z.string().min(1),
  token: z.string().min(1),
});

export async function POST(req: NextRequest) {
  try {
    const parsed = Schema.safeParse(await req.json());
    if (!parsed.success)
      return NextResponse.json({ error: "Invalid" }, { status: 400 });

    const { id, token } = parsed.data;
    const tokenHash = sha256(token);

    const ml = await prisma.invoiceBatchMagicLink.findUnique({ where: { id } });
    if (!ml || ml.tokenHash !== tokenHash || ml.expiresAt < new Date()) {
      return NextResponse.json(
        { error: "Token ungültig oder abgelaufen" },
        { status: 401 }
      );
    }

    // optional: Nutzungshäufigkeit tracken
    await prisma.invoiceBatchMagicLink.update({
      where: { id: ml.id },
      data: { usedCount: { increment: 1 }, lastUsedAt: new Date() },
    });

    // Session-Cookie setzen (für spätere Downloads ohne Token im Query)
    cookies().set("invoice_batch_auth", ml.id, {
      httpOnly: true,
      sameSite: "lax",
      secure: true,
      path: "/",
      maxAge: 7 * 24 * 60 * 60,
    });

    // Rechnungen zur E-Mail
    const invoices = await prisma.invoice.findMany({
      where: { mailToSend: { equals: ml.email, mode: "insensitive" } },
      orderBy: { invoice_date: "desc" },
    });

    // Map wie bei dir
    const invoiceInfos = invoices.map((invoice) => ({
      id: invoice.id,
      invoice_number: invoice.invoice_number,
      invoice_date: invoice.invoice_date,
      sum_gross: invoice.sum_gross,
      sum_net: invoice.sum_net,
      sum_tax: invoice.sum_tax,
      status: invoice.status,
      currency: invoice.currency,
      service_period_from: invoice.service_period_from,
      service_period_to: invoice.service_period_to,
      local_start_datetime: invoice.local_start_datetime,
      local_end_datetime: invoice.local_end_datetime,
      mailToSend: invoice.mailToSend,
      authorization_reference: invoice.authorization_reference,
    }));

    return NextResponse.json(
      { email: ml.email, invoices: invoiceInfos },
      { status: 200 }
    );
  } catch (e) {
    return NextResponse.json({ error: "Serverfehler" }, { status: 500 });
  }
}
