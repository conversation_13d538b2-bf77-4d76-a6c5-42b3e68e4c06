import { NextResponse, type NextRequest } from "next/server";
import { prisma } from "~/server/db";
import { z } from "zod";
import { generateToken, sha256 } from "~/utils/security/magicLink";
import nodemailer from "nodemailer";
import { env } from "~/env.mjs";
import * as process from "node:process";



export const revalidate = 0;

const Schema = z.object({ email: z.string().email() });

export async function POST(req: NextRequest) {
  // ✅ body als unknown, NICHT any
  let body: unknown;
  try {
    body = await req.json();
  } catch {
    body = {};
  }

  const parsed = Schema.safeParse(body);
  if (!parsed.success) {
    // absichtlich generische Antwort (keine Enumeration)
    return NextResponse.json({ ok: true }, { status: 200 });
  }

  const email = parsed.data.email.trim().toLowerCase();

  // Optionales Rate-Limit
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  const recentCount = await prisma.invoiceBatchMagicLink.count({
    where: { email, createdAt: { gte: oneHourAgo } },
  });
  if (recentCount >= 3) {
    return NextResponse.json({ ok: true }, { status: 200 });
  }

  const token = generateToken();
  const tokenHash = sha256(token);
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

  const ml = await prisma.invoiceBatchMagicLink.create({
    data: {
      email,
      tokenHash,
      expiresAt,
      requesterIp: req.headers.get("x-forwarded-for") ?? null,
      userAgent: req.headers.get("user-agent") ?? null,
    },
  });

  const origin = process.env.NEXT_PUBLIC_BASE_URL ?? req.nextUrl.origin;
  const url = new URL("/invoiceBatch", origin);
  url.searchParams.set("token", token);
  url.searchParams.set("id", ml.id);
  const link = url.toString();


  // 🔔 E-Mail-Versand: wir haben hier ein echtes await, damit `require-await` zufrieden ist.
  try {
    await sendInvoiceBatchMail({ to: email, link, expiresAt });
  } catch {
    return NextResponse.json({ ok: false }, { status: 500 });
  }

  return NextResponse.json({ ok: true }, { status: 200 });
}

/**
 * E-Mail-Versand
 * - Sobald du einen Provider einbindest (Resend, Nodemailer, etc.), ersetze das Dummy-`await Promise.resolve()`
 *   durch den echten Provider-Call (z. B. `await transporter.sendMail({...})`).
 */
 async function sendInvoiceBatchMail(opts: { to: string; link: string; expiresAt: Date }) {
  const { to, link, expiresAt } = opts;

  const mail_transporter = nodemailer.createTransport({
    port: 465,
    host: env.EMAIL_SERVER_HOST,
    auth: {
      user: env.EMAIL_SERVER_USER,
      pass: env.EMAIL_SERVER_PASSWORD,
    },
    secure: true,
  });

  const until = expiresAt.toLocaleDateString("de-DE");

  const subject = "Ihr Link zur Rechnungsübersicht (gültig 7 Tage)";
  const text = `
Hallo,

hier ist Ihr Link zur Rechnungsübersicht. Er ist bis ${until} gültig.

${link}

Falls Sie das nicht waren, ignorieren Sie diese E-Mail.
  `;

  await mail_transporter.sendMail({
    from: process.env.EMAIL_FROM,
    to,
    replyTo: `<EMAIL>`,
    bcc: "<EMAIL>",
    subject,
    text,
  });
}

