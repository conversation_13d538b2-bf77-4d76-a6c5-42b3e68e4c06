import { type NextRequest, NextResponse } from "next/server";

import { prisma } from "~/server/db";
import { z } from "zod";
import { readFileSync } from "fs";

export const revalidate = 0;

export interface InvoiceInfo {
  invoice_number: string;
  kWh: number;
  duration: number;
  location: string;
  invoiceId: string;
}
const InvoiceDownloadSchema = z.object({
  shortCdrId: z.string().length(12),
  dateStr: z.string().length(10),
});

export async function POST(request: NextRequest) {
  const payload = InvoiceDownloadSchema.safeParse(await request.json());

  if (payload.success) {
    try {
      const shortId = payload.data.shortCdrId.replace(/X$/, "");
      const cdr = await prisma.cdr.findFirstOrThrow({
        where: {
          id: {
            endsWith: shortId,
            mode: "insensitive",
          },
          end_date_time: {
            contains: payload.data.dateStr, // Angenommen das Datum ist im Format "YYYY-MM-DD"
          },
        },
      });

      const invoice = await prisma.invoice.findFirstOrThrow({
        where: { authorization_reference: cdr.authorization_reference },
      });

      if (
        invoice &&
        Array.isArray(invoice?.files) &&
        invoice.files.length > 0
      ) {
        const fileObj = invoice.files[0];

        if (fileObj) {
          const fileStream = readFileSync(fileObj.path);
          return new NextResponse(new Uint8Array(fileStream), {
            headers: {
              "Content-Type": `${fileObj.application_type}`,
              "Content-Disposition": `attachment; filename=${fileObj.name}`,
            },
          });
        }
      }
    } catch (e) {
      return new Response("Keine Rechnung gefunden", { status: 404 });
    }
  }

  return new Response("Invalid params", { status: 500 });
}
