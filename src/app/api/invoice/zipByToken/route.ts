import { NextRequest } from "next/server";
import { prisma } from "~/server/db";
import { cookies } from "next/headers";
import archiver from "archiver";
import { Readable } from "stream";
import { statSync, existsSync } from "fs";

export const runtime = "nodejs";
export const revalidate = 0;

export async function GET(req: NextRequest) {
  try {
    const cookie = cookies().get("invoice_batch_auth");
    const mlId = cookie?.value;
    console.log("ZIP Debug - Cookie:", cookie);
    console.log("ZIP Debug - mlId:", mlId);
    if (!mlId) return new Response("Nicht autorisiert", { status: 401 });

    const ml = await prisma.invoiceBatchMagicLink.findUnique({
      where: { id: mlId },
    });
    console.log("ZIP Debug - Magic Link:", ml);
    if (!ml || ml.expiresAt < new Date()) {
      console.log("ZIP Debug - Link ungültig oder abgelaufen");
      return new Response("Link ungültig oder abgelaufen", { status: 401 });
    }

    const invoices = await prisma.invoice.findMany({
      where: { mailToSend: { equals: ml.email, mode: "insensitive" } },
      orderBy: { invoice_date: "desc" },
    });
    console.log("ZIP Debug - Invoices found:", invoices.length);

    if (!invoices.length) {
      console.log("ZIP Debug - Keine Rechnungen vorhanden");
      return new Response("Keine Rechnungen vorhanden", { status: 404 });
    }

    // Archiver-Stream vorbereiten
    const archive = archiver("zip", { zlib: { level: 9 } });

    let filesAdded = 0;

    // Dateien hinzufügen (nur PDFs, vorhandene Pfade)
    for (const inv of invoices) {
      const files = Array.isArray(inv.files) ? inv.files : [];
      for (const file of files) {
        const name = `invoice_${inv.invoice_number ?? inv.id}.pdf`;
        if (existsSync(file.path)) {
          try {
            archive.file(file.path, { name });
            filesAdded++;
          } catch (err) {
            console.error(
              `Fehler beim Hinzufügen der Datei ${file.path}:`,
              err
            );
          }
        }
      }
    }

    // Falls nichts hinzugefügt wurde
    if (filesAdded === 0) {
      archive.append("Keine PDF-Dateien gefunden", { name: "README.txt" });
    }

    // Finalisieren OHNE await - das passiert asynchron
    void archive.finalize();

    const fileName = `invoices_${new Date().toISOString().slice(0, 10)}.zip`;

    // Stream direkt als ReadableStream wrappen für Next.js
    const stream = new ReadableStream({
      start(controller) {
        archive.on("data", (chunk) => {
          controller.enqueue(chunk);
        });

        archive.on("end", () => {
          controller.close();
        });

        archive.on("error", (err) => {
          console.error("Archive error:", err);
          controller.error(err);
        });
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "application/zip",
        "Content-Disposition": `attachment; filename=${fileName}`,
      },
    });
  } catch (e) {
    console.error("ZIP-Erstellungsfehler:", e);
    return new Response(
      `Serverfehler beim Erstellen des ZIP: ${
        e instanceof Error ? e.message : "Unbekannter Fehler"
      }`,
      { status: 500 }
    );
  }
}
