import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { StatusEnum } from "../../../../../../../../prisma/client";
import { validateToken } from "~/utils/ocpiAuth/ocpiAuthUtil";
import { prisma } from "~/server/db";
import { SendDebugMessage, SendInfoMessage } from "~/utils/chat/sendMessage";
import { fetchSessionFromApiBackend } from "~/utils/data/getBy";
import { convertSessionData, LongshipSession } from "~/utils/convert/api2ocpi";

export const revalidate = 0;

const PayloadSchema = z.object({
  result: z.enum([
    "ACCEPTED",
    "REJECTED",
    "FAILED",
    "TIMEOUT",
    "EVSE_INOPERATIVE",
    "EVSE_OCCUPIED",
    "NOT_SUPPORTED",
  ]),
  message: z.array(
    z.object({
      language: z.string(),
      text: z.string(),
    })
  ),
});

export async function POST(
  request: NextRequest,
  params: { params: { authorizationReference: string } }
) {
  if (!validateToken(request)) {
    return new NextResponse(
      JSON.stringify({ success: false, message: "authentication failed" }),
      { status: 401, headers: { "content-type": "application/json" } }
    );
  }

  const payload = PayloadSchema.safeParse(await request.json());

  if (payload.success) {
    let status: StatusEnum = StatusEnum.REFUNDED;
    if (payload.data.result == "ACCEPTED") {
      status = StatusEnum.START_ACCEPTED;
    } else {
      void (await SendDebugMessage(
        `POST longship/command/response/startSession data.result =  ${payload.data.result}}`
      ));
    }

    const paymentIndent = await prisma.paymentIntent.update({
      where: {
        authorization_reference: params.params.authorizationReference,
      },
      data: {
        status: status,
      },
    });

    if (!paymentIndent) {
      void (await SendInfoMessage(
        `POST longship/command/response/startSession PaymentIntent not found authorizationReference: ${params.params.authorizationReference}}`
      ));
      return NextResponse.json({
        status_code: 3000,
        status_message: "PaymentIndent not found",
        timestamp: new Date().toISOString(),
      });
    }

    if (status == StatusEnum.REFUNDED) {
      void (await SendInfoMessage(
        `Session Start timeout für authorizationReference: ${params.params.authorizationReference}`
      ));
    }
    const sessionReceived = await prisma.session.findFirst({
      where: { authorization_reference: params.params.authorizationReference },
    });
    if (!sessionReceived) {
      void (await SendInfoMessage(
        `Session not received yet. Trying to fetch via API: ${params.params.authorizationReference}`
      ));
      const session = (await fetchSessionFromApiBackend(
        "",
        params.params.authorizationReference
      )) as unknown as LongshipSession;
      if (!session) {
        void (await SendInfoMessage(
          `Session not found via API: ${params.params.authorizationReference}`
        ));
        return NextResponse.json({
          status_code: 1000,
          status_message: status,
          timestamp: new Date().toISOString(),
        });
      }
      void (await SendInfoMessage(
        `Session for ${params.params.authorizationReference} fetched from API`
      ));
      let converted;
      try {
        converted = convertSessionData(session);
      } catch (e) {
        void (await SendInfoMessage(
          `Convert for Session ${params.params.authorizationReference} failed`
        ));
        return NextResponse.json({
          status_code: 1000,
          status_message: status,
          timestamp: new Date().toISOString(),
        });
      }
      // check session does not exist and if not convert it to mongo format (ocpi format)
      // and add it to DB
      try {
        const sessionFromDb = await prisma.session.findUnique({
          where: { id: session.id },
        });

        if (!sessionFromDb && converted) {
          {
            const newSession = await prisma.session.create({ data: converted });
            if (newSession) {
              void (await SendInfoMessage(
                `Session for ${params.params.authorizationReference} created with workaround`
              ));
              const paymentIndent = await prisma.paymentIntent.update({
                where: {
                  authorization_reference: params.params.authorizationReference,
                },
                data: {
                  status: StatusEnum.SESSION_RECEIVED,
                },
              });
            } else {
              void (await SendInfoMessage(
                `Session create workaround for ${params.params.authorizationReference} failed (prisma)`
              ));
            }
          }
        }
      } catch (e) {
        void (await SendInfoMessage(
          `Catch block for ${params.params.authorizationReference}. Workaround error`
        ));
      }
    }

    //refunding bei session start ausgestellt
    /*if (status === StatusEnum.REFUNDED) {
      await stripe.paymentIntents.update(paymentIndent.id, {
        metadata: {
          cancelBy: `startSession ${payload.data.result}`,
        },
      });
      const cancelPaymentIntent = await stripe.paymentIntents.cancel(
        paymentIndent.id,
        { cancellation_reason: "abandoned" }
      );
      void (await SendInfoMessage(
        `Refunded paymentIntent ${cancelPaymentIntent.id} after start session rejected`
      ));

      void (await prisma.paymentIntent.update({
        where: {
          authorization_reference: params.params.authorizationReference,
        },
        data: {
          status: status,
        },
      }));
    }*/

    return NextResponse.json({
      status_code: 1000,
      status_message: status,
      timestamp: new Date().toISOString(),
    });
  } else {
    void (await SendInfoMessage(
      `Unknown payload form ocpi START_SESSION callback`,
      {
        zodError: payload.error,
      }
    ));
  }

  return NextResponse.json({
    status_code: 3000,
    status_message: "Error",
    timestamp: new Date().toISOString(),
  });
}
