import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { StatusEnum } from "../../../../../../../../prisma/client";
import { validateToken } from "~/utils/ocpiAuth/ocpiAuthUtil";
import { prisma } from "~/server/db";
import { SendInfoMessage } from "~/utils/chat/sendMessage";
export const revalidate = 0;

const PayloadSchema = z.object({
  result: z.enum(["ACCEPTED", "REJECTED"]),
  message: z.array(
    z.object({
      language: z.string(),
      text: z.string(),
    })
  ),
});

export async function POST(
  request: NextRequest,
  params: { params: { authorizationReference: string } }
) {
  if (!validateToken(request)) {
    return new NextResponse(
      JSON.stringify({ success: false, message: "authentication failed" }),
      { status: 401, headers: { "content-type": "application/json" } }
    );
  }

  const payload = PayloadSchema.safeParse(await request.json());

  if (payload.success) {
    let status: StatusEnum = StatusEnum.STOP_SESSION_REJECTED;
    if (payload.data.result == "ACCEPTED") {
      status = StatusEnum.STOP_SESSION_ACCEPTED;
    }

    const paymentIndent = await prisma.paymentIntent.update({
      where: {
        authorization_reference: params.params.authorizationReference,
      },
      data: {
        status: status,
      },
    });

    if (!paymentIndent) {
      void (await SendInfoMessage(
        `PaymentIntent not found ${params.params.authorizationReference}`
      ));
      return NextResponse.json({
        status_code: 3000,
        status_message: "PaymentIndent not found",
        timestamp: new Date().toISOString(),
      });
    }

    return NextResponse.json({
      status_code: 1000,
      status_message: status,
      timestamp: new Date().toISOString(),
    });
  } else {
    void (await SendInfoMessage(
      `Unknown payload from ocpi STOP_SESSION callback`,
      {
        zodError: payload.error,
      }
    ));
  }

  return NextResponse.json({
    status_code: 3000,
    status_message: "Error",
    timestamp: new Date().toISOString(),
  });
}
