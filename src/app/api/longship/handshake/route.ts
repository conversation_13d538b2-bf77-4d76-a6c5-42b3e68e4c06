import { type NextRequest, NextResponse } from "next/server";

import { prisma } from "~/server/db";
import { z } from "zod";
import { env } from "~/env.mjs";
export const revalidate = 0;

/*

GET https://beta.ocpi.longship.io/ocpi/2.2/cdrs/?date_from=2023-01-01T12:00:00&date_to=2024-01-01T12:00:00&limit=100&offset=0
Authorization: Token 3e59995197ab41b78b8b5eec1256128c
Content-Type: application/json

 */

const CPOBusinessDetailsSchema = z.object({
  name: z.string(),
});

const CPORoleSchema = z.object({
  role: z.literal("CPO"),
  business_details: CPOBusinessDetailsSchema,
  party_id: z.string(),
  country_code: z.string(),
});

const DataSchema = z.object({
  token: z.string(),
  url: z.string(),
  roles: z.array(CPORoleSchema),
});

const HandshakeSchema = z.object({
  data: DataSchema,
  status_code: z.number(),
  status_message: z.string(),
  timestamp: z.string(),
});

export async function GET(_request: NextRequest) {
  const url = `${env.LONGSHIP_DOMAIN}/ocpi/2.2/credentials`;
  const initToken = "1f49a613beaf44ec9900c26ee8ee1e95"; // longship init token

  const empToken = "ffff05f2c47d60bdac8f4f2b4dde0bbc"; // unser eigener Token
  const data = {
    url: `https://test.adhoc.eulektro.de/ocpi/versions`,
    token: empToken,
    roles: [
      {
        role: "EMSP",
        party_id: "EUL",
        country_code: "DE",
        business_details: {
          name: "Eulektro GmbH",
        },
      },
    ],
  };

  const options = {
    method: "POST",
    headers: {
      Authorization: `Token ${initToken}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  };

  const response = await fetch(url, options);

  const payload = HandshakeSchema.safeParse(await response.json());

  if (payload.success) {
    await prisma.handshake.create({
      data: {
        emp_token: empToken,
        cpo_token: payload.data.data.token,
        url: payload.data.data.url,
      },
    });

    return NextResponse.json({
      status_code: 1000,
      status_message: "Success",
      timestamp: new Date().toISOString(),
    });
  } else {
    return NextResponse.json({
      status_code: 3000,
      status_message: "Error",
      timestamp: new Date().toISOString(),
    });
  }
}
