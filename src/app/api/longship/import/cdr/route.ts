import { type NextRequest, NextResponse } from "next/server";

import { prisma } from "~/server/db";

import { z } from "zod";
import { CdrSchema } from "../../../../../../prisma/generated/zod";
import { env } from "~/env.mjs";
import { CpoHeaders } from "~/utils/cpo/header";

export const revalidate = 0;

const RequestSchema = z
  .object({
    data: z.array(CdrSchema),
    status_code: z.number(),
    status_message: z.string(),
    timestamp: z.string(),
  })
  .nonstrict();

/*

GET https://beta.ocpi.longship.io/ocpi/2.2/cdrs/?date_from=2023-01-01T12:00:00&date_to=2024-01-01T12:00:00&limit=100&offset=0
Authorization: Token 3e59995197ab41b78b8b5eec1256128c
Content-Type: application/json

 */

// get ALL cdrs from CPO Backend. This is a one time import
export async function GET(_request: NextRequest) {
  const data = await fetch(
    `${env.LONGSHIP_DOMAIN}/ocpi/2.2/cdrs/?date_from=2023-01-01T12:00:00&date_to=2024-01-01T12:00:00&limit=100&offset=0`,
    {
      headers: CpoHeaders,
    }
  );

  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const payload = await data.json();

  const result = RequestSchema.safeParse(payload);

  if (result.success) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    void (await prisma.cdr.createMany({ data: result.data.data })); // ToDo find better way to check types
    return NextResponse.json({
      data: "ok",
      status_code: 1000,
      status_message: "Success",
      timestamp: new Date().toISOString(),
    });
  }
  return NextResponse.json({
    data: "ok",
    status_code: 3000,
    status_message: "Error",
    timestamp: new Date().toISOString(),
  });
}
