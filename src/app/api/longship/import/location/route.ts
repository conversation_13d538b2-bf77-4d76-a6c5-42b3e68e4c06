import { type NextRequest, NextResponse } from "next/server";
import { env } from "~/env.mjs";
import { CpoHeaders } from "~/utils/cpo/header";
import { LocationsResponseSchema } from "~/utils/schema/location";

import { prisma } from "~/server/db";
import {
  PrismaClientKnownRequestError,
  PrismaClientValidationError,
} from "@prisma/client/runtime/library";

export const revalidate = 0;
// get all locations from longship and create or replace them in our database
export async function GET(_request: NextRequest) {
  const url = `${env.LONGSHIP_DOMAIN}/ocpi/2.2/locations`;

  const response = await fetch(url, { headers: CpoHeaders });
  const locationsJson: unknown = await response.json();
  const locations = LocationsResponseSchema.safeParse(locationsJson);

  if (locations.success) {
    const oldLocations = await prisma.location.findMany({
      select: { id: true },
    });

    const transactionCommands = [];
    for (const location of locations.data.data) {
      if (oldLocations.find((oldLocation) => oldLocation.id === location.id)) {
        transactionCommands.push(
          prisma.location.delete({
            where: {
              id: location.id,
            },
          })
        );
      }

      transactionCommands.push(
        prisma.location.create({
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          data: location,
        })
      );
    }

    try {
      void (await prisma.$transaction(transactionCommands));
    } catch (e) {
      if (e instanceof PrismaClientValidationError) {
        return NextResponse.json({
          data: {
            code: e.name,
            error: e.message,
          },
          status_code: 3000,
          status_message: "Prisma Validation Error",
          timestamp: new Date().toISOString(),
        });
      } else if (e instanceof PrismaClientKnownRequestError) {
        return NextResponse.json({
          data: {
            code: e.name,
            error: e.message,
          },
          status_code: 3000,
          status_message: "Prisma KnownRequest Error",
          timestamp: new Date().toISOString(),
        });
      } else {
        return NextResponse.json({
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          data: e.message,
          status_code: 3000,
          status_message: "Unknow Prisma Error",
          timestamp: new Date().toISOString(),
        });
      }
    }
    return NextResponse.json({
      status_code: 1000,
      status_message: "Success",
      timestamp: new Date().toISOString(),
    });
  }

  return NextResponse.json({
    status_code: 3000,
    status_message: "Error",
    timestamp: new Date().toISOString(),
  });
}
