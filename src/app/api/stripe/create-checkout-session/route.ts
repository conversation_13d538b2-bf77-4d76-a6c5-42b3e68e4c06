import { type NextRequest, NextResponse } from "next/server";
import { env } from "~/env.mjs";
import { prisma } from "~/server/db";
import { stripe } from "~/utils/stripe/stripe";

export const revalidate = 0;
export async function POST(_request: NextRequest) {
  // ToDo get EMP from chargingPoint to get the right Stripe Account
  const emp = await prisma.emp.findFirst({
    where: {
      party_id: "EUL",
      country_code: "DE",
    },
  });
  if (!emp) {
    return NextResponse.json(
      { success: false, message: "EMP not found" },
      { status: 404 }
    );
  }
  let amountToBlock = 200; // 200 = 2€
  if (env.NODE_ENV === "production") {
    amountToBlock = emp.amount_to_block;
  }

  const paymentIntent = await stripe.paymentIntents.create({
    payment_method_types: ["card"],
    currency: "EUR",
    capture_method: "manual",
    amount: amountToBlock,
  });

  return NextResponse.json({
    success: true,
    clientSecret: paymentIntent.client_secret,
    paymentIntentId: paymentIntent.id,
    amountToBlock: amountToBlock,
  });
}
