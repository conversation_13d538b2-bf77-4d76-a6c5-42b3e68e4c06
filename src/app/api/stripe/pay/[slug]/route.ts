import { type NextRequest, NextResponse } from "next/server";
import { stripe } from "~/utils/stripe/stripe";

interface Props {
  params: {
    slug: string;
  };
}

export const revalidate = 0;

export async function GET(request: NextRequest, { params }: Props) {
  const paymentIntentId = params.slug;

  // Erfassen der Zahlung
  const captured_payment_intent = await stripe.paymentIntents.capture(
    paymentIntentId,
    { amount_to_capture: 1500 }
  );

  return NextResponse.json(captured_payment_intent);
}
