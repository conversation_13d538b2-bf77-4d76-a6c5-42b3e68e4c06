import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { prisma } from "~/server/db";
import { sendInvoice } from "~/utils/sendInvoice/sendInvoice";

import { PrismaClientKnownRequestError } from "@prisma/client/runtime/library";
import { SendErrorMessage } from "~/utils/chat/sendMessage";

const PayloadSchema = z.object({
  email: z.string().email(),
  paymentIntentId: z.string(),
});

export const revalidate = 0;
export const POST = async (request: NextRequest) => {
  const payload = PayloadSchema.safeParse(await request.json());

  if (!payload.success) {
    return NextResponse.json(
      {
        status_code: 3000,
        status_message: "Payload not expected",
        timestamp: new Date().toISOString(),
      },
      { status: 400 }
    );
  }

  // paymentInten holen und schauen ob schon ein CDR existiert
  // wenn ja darf keine invoiceMail vorhanden sein, sonst wurde schon eine mail gesendet
  const paymmentIntent = await prisma.paymentIntent.findUnique({
    where: { id: payload.data.paymentIntentId },
    include: { cdr: true },
  });
  if (!paymmentIntent) {
    void (async () => {
      await SendErrorMessage(
        `PaymentIntent: ${payload.data.paymentIntentId} nicht gefunden`
      );
    })();
    return NextResponse.json({}, { status: 404 });
  }
  // wenn ein cdr da ist aber keine invoiceMail eingegeben wurde, dann schicke rechnung nochmal raus
  if (paymmentIntent?.cdrId && !paymmentIntent?.invoiceMail) {
    const invoiceArray = await prisma.invoice.findRaw({
      filter: {
        "metadata.paymentIntent": payload.data.paymentIntentId,
      },
    });
    if (Array.isArray(invoiceArray) && invoiceArray.length > 0) {
      const invoiceObj = invoiceArray[0] as unknown as {
        _id: { $oid: string };
      };

      try {
        const invoice = await prisma.invoice.update({
          where: { id: invoiceObj._id.$oid },
          data: { mailToSend: payload.data.email },
        });
        const mailSent = sendInvoice(invoice);
      } catch (e) {
        if (e instanceof PrismaClientKnownRequestError) {
          void (async () => {
            await SendErrorMessage(
              `Fehler beim nachträglichen Email Versand ${e.message}`
            );
          })();
          return NextResponse.json({ message: e.message }, { status: 500 });
        }
        void (async () => {
          await SendErrorMessage(`Fehler beim nachträglichen Email Versand`);
        })();
        return NextResponse.json({}, { status: 500 });
      }
    }
  } else {
    if (!paymmentIntent?.invoiceMail) {
      //only update paymentIntent if no cdr has been created
      void (await prisma.paymentIntent.update({
        where: {
          id: payload.data.paymentIntentId,
        },
        data: {
          invoiceMail: payload.data.email,
        },
      }));
    }
  }

  return NextResponse.json({}, { status: 200 });
};
