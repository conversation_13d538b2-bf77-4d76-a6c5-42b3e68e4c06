import { type NextRequest, NextResponse } from "next/server";
import { syncSingleCdrById } from "~/utils/cdrSync";
import { SendInfoMessage } from "~/utils/chat/sendMessage";

export const revalidate = 0;

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const cdrId = params.id;
  const token = request.nextUrl.searchParams.get("token");
  
  // Einfache Authentifizierung mit Token
  if (token !== "490327urzh0iqwg3f32mf03q2ne") {
    return NextResponse.json({
      status_code: 3000,
      status_message: "Unauthorized",
      timestamp: new Date().toISOString(),
    }, { status: 401 });
  }
  
  if (!cdrId) {
    return NextResponse.json({
      status_code: 3000,
      status_message: "Missing CDR ID",
      timestamp: new Date().toISOString(),
    });
  }
  
  await SendInfoMessage(`Starting sync for single CDR with ID: ${cdrId}`);
  
  const syncResult = await syncSingleCdrById(cdrId);
  
  if (syncResult.successful > 0) {
    await SendInfoMessage(`Successfully synchronized CDR with ID: ${cdrId}`);
    return NextResponse.json({
      data: {
        cdrId: cdrId,
        status: "success"
      },
      status_code: 1000,
      status_message: "Success",
      timestamp: new Date().toISOString(),
    });
  } else {
    await SendInfoMessage(`Failed to synchronize CDR with ID: ${cdrId}`);
    return NextResponse.json({
      data: {
        cdrId: cdrId,
        status: "failed",
        errors: syncResult.errors
      },
      status_code: 3000,
      status_message: syncResult.errors[0] || "Failed to synchronize CDR",
      timestamp: new Date().toISOString(),
    });
  }
}