import { type NextRequest, NextResponse } from "next/server";
import { syncCdrsFromBackend } from "~/utils/cdrSync";
import { SendInfoMessage } from "~/utils/chat/sendMessage";

export const revalidate = 0;

export async function GET(request: NextRequest) {
  const dateFromParam = request.nextUrl.searchParams.get("date_from");
  const dateToParam = request.nextUrl.searchParams.get("date_to");
  const offsetParam = request.nextUrl.searchParams.get("offset") || "0";
  const limitParam = request.nextUrl.searchParams.get("limit") || "100";
  const token = request.nextUrl.searchParams.get("token");
  
  // Einfache Authentifizierung mit Token
  if (token !== "490327urzh0iqwg3f32mf03q2ne") {
    return NextResponse.json({
      status_code: 3000,
      status_message: "Unauthorized",
      timestamp: new Date().toISOString(),
    }, { status: 401 });
  }
  
  if (!dateFromParam || !dateToParam) {
    return NextResponse.json({
      status_code: 3000,
      status_message: "Missing date_from or date_to parameters",
      timestamp: new Date().toISOString(),
    });
  }
  
  const dateFrom = new Date(dateFromParam);
  const dateTo = new Date(dateToParam);
  const offset = parseInt(offsetParam, 10);
  const limit = parseInt(limitParam, 10);
  
  if (isNaN(dateFrom.getTime()) || isNaN(dateTo.getTime())) {
    return NextResponse.json({
      status_code: 3000,
      status_message: "Invalid date format",
      timestamp: new Date().toISOString(),
    });
  }
  
  await SendInfoMessage(`Starting CDR sync from ${dateFrom.toISOString()} to ${dateTo.toISOString()}`);
  
  const syncResult = await syncCdrsFromBackend(dateFrom, dateTo, offset, limit);
  
  await SendInfoMessage(`CDR sync completed: ${syncResult.successful} successful, ${syncResult.failed} failed out of ${syncResult.totalProcessed} total`);
  
  return NextResponse.json({
    data: {
      totalProcessed: syncResult.totalProcessed,
      successful: syncResult.successful,
      failed: syncResult.failed,
      errors: syncResult.errors.slice(0, 10) // Nur die ersten 10 Fehler zurückgeben
    },
    status_code: syncResult.failed > 0 ? 2000 : 1000, // Teilweiser Erfolg oder voller Erfolg
    status_message: syncResult.failed > 0 ? "Partial success" : "Success",
    timestamp: new Date().toISOString(),
  });
}