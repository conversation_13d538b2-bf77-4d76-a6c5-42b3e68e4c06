import React from "react";

import { InvoiceDownloadForm } from "~/app/invoice/InvoiceDownloadForm";
import Image from "next/image";

const Page = () => {
  return (
    <body className={"bg-stone-200"}>
      <div className={"p-1"}>
        <div className={"flex flex-col gap-3 p-4 sm:flex-row sm:items-center"}>
          <Image
            src={"/commonImages/EULEKTRO_schwarz.svg"}
            priority={false}
            width={200}
            height={100}
            alt={"Eulektro logo"}
            className={""}
          />
          <span className={"sm:w-1/2"}>
            Here you can download your invoice based on the bank transaction
            reference and date of the charging session
          </span>
        </div>
        <InvoiceDownloadForm />
      </div>
    </body>
  );
};

export default Page;
