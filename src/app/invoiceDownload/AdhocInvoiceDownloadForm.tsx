"use client";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { FaDownload, FaSpinner } from "react-icons/fa";

interface FormValues {
  dateStr: string; // YYYY-MM-DD
  amount: string; // entered as string, converted to number
}

export default function AdhocInvoiceDownloadForm() {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<FormValues>();

  const [apiError, setApiError] = useState<string>("");

  const onSubmit = async (data: FormValues) => {
    setApiError("");
    try {
      const amount = parseFloat(data.amount);
      const res = await fetch("/api/invoice/downloadByDateAmountFromToken", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          dateStr: data.dateStr,
          amount,
        }),
      });
      
      if (res.ok) {
        // Download the PDF
        const blob = await res.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", `Rechnung_${data.dateStr}_${amount.toFixed(2)}.pdf`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        
        // Reset form
        reset({ dateStr: "", amount: "" });
      } else {
        const errorText = await res.text();
        setApiError(errorText || "Download fehlgeschlagen");
      }
    } catch (e) {
      setApiError("Serverfehler");
    }
  };

  return (
    <div className="w-full">
      <h2 className="text-xl font-bold text-black mb-4">Adhoc-Rechnung herunterladen</h2>
      <p className="text-gray-700 mb-4">
        Laden Sie Ihre Adhoc-Rechnung direkt herunter, indem Sie Datum und den exakten Betrag eingeben.
      </p>
      
      {/* eslint-disable-next-line @typescript-eslint/no-misused-promises */}
      <form onSubmit={handleSubmit(onSubmit)} >
        <div className="flex flex-col md:flex-row md:items-end md:gap-4">
          <div className="min-w-0 md:flex-1">
            <label
              className="mb-2 block text-xl font-bold text-black"
              htmlFor="dateStr"
            >
              Datum
            </label>
            <input
              id="dateStr"
              type="date"
              className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-700 focus:border-elm-600 focus:outline-none"
              {...register("dateStr", {
                required: "Dieses Feld ist erforderlich",
              })}
            />
            {errors.dateStr && (
              <p className="mt-1 text-sm text-red-600">
                {errors.dateStr.message}
              </p>
            )}
          </div>

          <div className="min-w-0 md:flex-1">
            <label
              className="mb-2 block text-xl font-bold text-black"
              htmlFor="amount"
            >
              Betrag
            </label>
            <input
              id="amount"
              type="number"
              inputMode="decimal"
              step="0.01"
              placeholder="z.B. 12.34"
              className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-700 focus:border-elm-600 focus:outline-none"
              {...register("amount", {
                required: "Dieses Feld ist erforderlich",
              })}
            />
            {errors.amount && (
              <p className="mt-1 text-sm text-red-600">
                {errors.amount.message}
              </p>
            )}
          </div>
        </div>
        <button
          className="mt-10 inline-flex shrink-0 items-center gap-2 self-start  bg-elm-700 px-4 py-2 text-white transition-colors hover:bg-elm-800"
          type="submit"
          disabled={isSubmitting}
        >
          Herunterladen{" "}
          {isSubmitting ? <FaSpinner className="animate-spin" /> : <FaDownload />}
        </button>
      </form>

      {/* Fehlermeldung */}
      {apiError && (
        <div className="mt-4">
          <p className="text-red-600">{apiError}</p>
        </div>
      )}
    </div>
  );
}
