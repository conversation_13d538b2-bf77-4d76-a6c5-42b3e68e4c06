"use client";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import React, { useState } from "react";
import { InvoiceInfo } from "~/app/api/invoice/list/route";
import { FaDownload, FaSearch, FaSpinner } from "react-icons/fa";
import Link from "next/link";

interface FormValues {
  shortCdrId: string;
  dateStr: string;
}

export const InvoiceDirectDownloadForm = () => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitSuccessful, isSubmitting },
  } = useForm<FormValues>();

  const [apiError, setApiError] = useState<string>("");
  const onSubmit: SubmitHandler<FormValues> = async (data: FormValues) => {
    setApiError("");
    const dateStr = new Date(data.dateStr).toISOString().substring(0, 10);
    try {
      const response = await fetch("/api/invoice/searchAndDownload", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ...data, dateStr: dateStr }),
      });

      if (response.status == 200) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "Invoice_"+data.shortCdrId );
        document.body.appendChild(link);
        link.click();
      } else {
        setApiError(await response.text());
      }
    } catch (error) {
      setApiError("Error when searching for invoices");
    }
  };

  return (
    <div className={"border-1 w-full border-gray-600 lg:w-1/2"}>
      {/* eslint-disable-next-line @typescript-eslint/no-misused-promises */}
      <form className={"w-full"} onSubmit={handleSubmit(onSubmit)}>
        <div className="flex flex-row items-center gap-2">
          <label
            className="w-100 whitespace-nowrap text-xl  font-bold text-black "
            htmlFor="shortCdrId"
          >
            ID / Zahlungsreferenz:
          </label>
          <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
            <input
              placeholder={"e.g. F123646FA1EX"}
              className="leading-5.6 block w-full appearance-none  rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal text-gray-700 outline-none transition-all placeholder:text-gray-300 focus:border-elm-600 focus:outline-none "
              id="shortCdrId"
              {...register("shortCdrId", {
                minLength: {
                  value: 12,
                  message: "Bitte genau 12 Zeichen eingeben (zu wenig)",
                },
                maxLength: {
                  value: 12,
                  message: "Bitte genau 12 Zeichen eingeben (zu viele)",
                },
                required: "Eine Zahlungsreferenz wird benötigt",
              })}
            />
            {errors.shortCdrId && (
              <p className={"text-red-600"}>{errors.shortCdrId.message}</p>
            )}
          </div>
        </div>

        <div className="mt-5 flex flex-row items-center gap-2 ">
          <div>
            <label
              className="w-100 whitespace-nowrap text-xl  font-bold text-black "
              htmlFor="dateStr"
            >
              Datum:
            </label>
            <span className={"text-xs"}> (beendet am)</span>
          </div>
          <div className={"md:w-1/2"}>
            <input
              className="leading-5.6 block appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal text-gray-700 outline-none transition-all placeholder:text-gray-300 focus:border-elm-600 md:w-1/2  "
              type="date"
              id="dateStr"
              {...register("dateStr", {
                required:
                  "Das Datum an dem der Ladevorgang beendet wurde wird benötigt",
              })}
            />
            {errors.dateStr && (
              <p className={"text-red-600"}>{errors.dateStr.message}</p>
            )}
          </div>
        </div>

        <div className={" mt-10 flex flex-row "}>
          <button
            className={
              "flex flex-row items-center  gap-2 bg-elm-700 px-4 py-2 text-white"
            }
            type="submit"
          >
            Herunterladen <FaDownload size={14} />{" "}
            {isSubmitting && <FaSpinner className="animate-spin" />}
          </button>
          {apiError && (<span className={"text-red-600 ml-3"}>{apiError}</span>)}
        </div>
      </form>


    </div>
  );
};
