"use client";
import { Submit<PERSON>and<PERSON>, useForm } from "react-hook-form";
import React, { useState } from "react";
import { InvoiceInfo } from "~/app/api/invoice/list/route";
import { FaDownload, FaSearch, FaSpinner } from "react-icons/fa";

interface FormValues {
  shortCdrId: string;
  dateStr: string;
}

export const InvoiceDownloadForm = () => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitSuccessful, isSubmitting },
  } = useForm<FormValues>();

  const [foundInvoice, setFoundInvoice] = useState<InvoiceInfo>();
  const [apiError, setApiError] = useState<string>("");
  const onSubmit: SubmitHandler<FormValues> = async (data: FormValues) => {
    setApiError("");
    const dateStr = new Date(data.dateStr).toISOString().substring(0, 10);
    try {
      const response = await fetch("/api/invoice/list", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ...data, dateStr: dateStr }),
      });

      if (response.status == 200) {
        const invoiceInfo = (await response.json()) as InvoiceInfo;
        setFoundInvoice(invoiceInfo);

        setApiError("");
      } else {
        setFoundInvoice(undefined);
        setApiError(response.statusText);
      }
    } catch (error) {
      setApiError("Error when searching for invoices");
      setFoundInvoice(undefined);
    }
  };

  return (
    <div className={"border-1 w-full border-gray-600 lg:w-1/2"}>
      {/* eslint-disable-next-line @typescript-eslint/no-misused-promises */}
      <form className={"w-full px-4"} onSubmit={handleSubmit(onSubmit)}>
        <div className="flex flex-col md:flex-row md:gap-2">
          <label
            className="w-100 whitespace-nowrap text-xl  font-bold text-black "
            htmlFor="shortCdrId"
          >
            ID / Reference:
          </label>
          <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
            <input
              placeholder={"e.g. CD27D00F123646FA1E7"}
              className="leading-5.6 block w-full appearance-none  rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal text-gray-700 outline-none transition-all placeholder:text-gray-300 focus:border-elm-600 focus:outline-none "
              id="shortCdrId"
              {...register("shortCdrId", {
                minLength: {
                  value: 12,
                  message: "Too short, expected 12 characters",
                },
                maxLength: {
                  value: 12,
                  message: "Too long, expected 12 characters",
                },
                required: "This field is required",
              })}
            />
            {errors.shortCdrId && (
              <p className={"text-red-600"}>{errors.shortCdrId.message}</p>
            )}
          </div>
        </div>

        <div className="mt-5 flex flex-col gap-2 md:flex-row md:gap-2">
          <div>
            <label
              className="w-100 whitespace-nowrap text-xl  font-bold text-black "
              htmlFor="dateStr"
            >
              Date:
            </label>
            <span className={"ml-2 text-xs"}> (of charging day)</span>
          </div>
          <div className={"md:w-1/2"}>
            <input
              className="leading-5.6 block appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal text-gray-700 outline-none transition-all placeholder:text-gray-300 focus:border-elm-600 md:w-1/2  "
              type="date"
              id="dateStr"
              {...register("dateStr", { required: "This field is required" })}
            />
            {errors.dateStr && (
              <p className={"text-red-600"}>{errors.dateStr.message}</p>
            )}
          </div>
        </div>

        <div className={" mt-2 flex flex-row md:mt-0"}>
          <button
            className={
              "flex flex-row items-center gap-2 rounded-md bg-elm-700 px-4 py-2 text-white"
            }
            type="submit"
          >
            Search <FaSearch size={14} />{" "}
            {isSubmitting && <FaSpinner className="animate-spin" />}
          </button>
        </div>
      </form>

      <div>
        {foundInvoice && (
          <>
            <div className={"flex flex-col p-4"}>
              <span className={"border-t-2 border-gray-600 text-xl font-bold"}>
                Folgende Rechnung wurde gefunden:
              </span>
              <div className="">
                <div className="flex items-center">
                  <span className="pr-2 text-right font-semibold">
                    Invoice Number:
                  </span>
                  <span className="">{foundInvoice.invoice_number}</span>
                </div>
                <div className="flex items-center ">
                  <span className=" pr-2 text-right font-semibold">
                    Energy:
                  </span>
                  <span className="">{foundInvoice.kWh} kWh</span>
                </div>
                <div className="flex items-center">
                  <span className=" pr-2 text-right font-semibold">
                    Location:
                  </span>
                  <span className="">{foundInvoice.location}</span>
                </div>
                <div className="flex items-center ">
                  <span className="pr-2 text-right font-semibold">
                    Duration:
                  </span>
                  <span className="">
                    {foundInvoice.duration.toFixed(2)} min
                  </span>
                </div>
              </div>
            </div>
            <div className={"flex flex-row px-4"}>
              <a
                href={`/api/invoice/download?invoiceId=${foundInvoice.invoiceId}`}
                className="flex flex-row items-center gap-2 rounded-md bg-elm-700 px-4 py-2 text-white"
              >
                Download <FaDownload size={14} />
              </a>
            </div>
          </>
        )}
        {isSubmitSuccessful && !foundInvoice && (
          <div className={"flex flex-col p-4"}>
            <span className={"border-t-2 border-gray-600 text-xl font-bold"}>
              Keine Rechnung gefunden!
            </span>
            {apiError && <span className={"text-red-600"}>{apiError}</span>}
          </div>
        )}
      </div>
    </div>
  );
};
