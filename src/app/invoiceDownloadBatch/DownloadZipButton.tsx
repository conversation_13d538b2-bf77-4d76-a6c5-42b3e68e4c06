"use client";
import React from "react";
import { useState } from "react";
import { FaDownload } from "react-icons/fa";

export default function DownloadZipButton() {
  const [loading, setLoading] = useState(false);

  const handleClick = async () => {
    try {
      setLoading(true);
      const res = await fetch("/api/invoice/zipByToken", { method: "GET" });
      if (!res.ok) {
        const msg =
          res.status === 401
            ? "Nicht autorisiert oder Link abgelaufen"
            : "Fehler beim Erstellen des ZIP";
        alert(msg);
        return;
      }
      const blob = await res.blob();
      let filename = "invoices.zip";
      const cd = res.headers.get("Content-Disposition");
      const m = cd?.match(/filename=([^;]+)$/i);
      if (m?.[1]) filename = m[1].replace(/\"/g, "").trim();

      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      a.remove();
      URL.revokeObjectURL(url);
    } catch (e) {
      alert("Unerwarteter Fehler beim Download");
    } finally {
      setLoading(false);
    }
  };

  return (
    <button
      onClick={() => void handleClick()}
      disabled={loading}
      className={`inline-flex w-full items-center justify-center gap-2 rounded px-3 py-2 text-sm text-white transition-colors sm:w-auto ${
        loading ? "bg-gray-400" : "bg-elm-700 hover:bg-elm-800"
      }`}
      title="Alle Rechnungen als ZIP herunterladen"
    >
      <FaDownload size={14} />
      <span className="whitespace-nowrap">
        {loading ? "Erstelle ZIP…" : "Alle als ZIP"}
      </span>
    </button>
  );
}
