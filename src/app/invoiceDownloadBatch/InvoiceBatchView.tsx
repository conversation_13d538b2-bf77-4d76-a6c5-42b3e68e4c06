// app/invoiceDownloadBatch/InvoiceBatchView.tsx
"use client";
import React, { useEffect, useState } from "react";
import type { InvoiceBatchInfo } from "~/app/types/invoice";
import { FaDownload } from "react-icons/fa";
import DownloadZipButton from "./DownloadZipButton";
// … deine Imports …
// oder dein gemeinsamer Typpfad

export async function downloadInvoiceViaShortId(invoice: InvoiceBatchInfo) {
  // 1) Stabiler Direkt-Download über invoiceId
  if (invoice.id) {
    try {
      const res = await fetch(
        `/api/invoice/download?invoiceId=${encodeURIComponent(invoice.id)}`,
        {
          method: "GET",
          headers: { Accept: "application/pdf" },
        }
      );
      if (res.ok) {
        const blob = await res.blob();
        let filename = "invoice.pdf";
        const cd = res.headers.get("Content-Disposition");
        const m = cd?.match(/filename="?([^"]+)"?/i);
        if (m?.[1]) filename = m[1];
        else if (invoice.invoice_number)
          filename = `${invoice.invoice_number}.pdf`;

        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        a.remove();
        URL.revokeObjectURL(url);
        return; // Erfolg
      } else {
        alert("Fehler beim Herunterladen der Rechnung: " + res.statusText);
        return;
      }
    } catch (e) {
      alert("Fehler beim Herunterladen der Rechnung: ");
    }
  }
}

const formatDate = (d: string | Date) =>
  new Date(d).toLocaleDateString("de-DE");
const formatCurrency = (amount: number, currency: string | null) =>
  new Intl.NumberFormat("de-DE", {
    style: "currency",
    currency: currency || "EUR",
  }).format(amount);
const getStatusText = (s: string) =>
  s === "DRAFT"
    ? "Entwurf"
    : s === "INMUTABLE_WRITTEN"
    ? "Erstellt"
    : s === "PAID"
    ? "Bezahlt"
    : s;
const getStatusColor = (s: string) =>
  s === "DRAFT"
    ? "text-yellow-600"
    : s === "INMUTABLE_WRITTEN"
    ? "text-blue-600"
    : s === "PAID"
    ? "text-green-600"
    : "text-gray-600";

export default function InvoiceBatchView({
  token,
  id,
}: {
  token?: string;
  id?: string;
}) {
  const [loading, setLoading] = useState(true);
  const [email, setEmail] = useState<string>("");
  const [invoices, setInvoices] = useState<InvoiceBatchInfo[]>([]);
  const [error, setError] = useState<string>("");

  useEffect(() => {
    const loadInvoiceData = async () => {
      if (!token || !id) {
        setError("Ungültiger Link.");
        setLoading(false);
        return;
      }
      try {
        const res = await fetch("/api/invoice/listByToken", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ token, id }),
        });
        if (res.ok) {
          const data = (await res.json()) as {
            email: string;
            invoices: InvoiceBatchInfo[];
          };
          setEmail(data.email);
          setInvoices(data.invoices);
        } else {
          setError("Link ungültig oder abgelaufen.");
        }
      } catch {
        setError("Serverfehler");
      } finally {
        setLoading(false);
      }
    };

    void loadInvoiceData();
  }, []);

  if (loading) return <div className="p-4">Lade…</div>;
  if (error) return <div className="p-4 text-red-600">{error}</div>;

  return (
    <div className="rounded-lg border border-gray-600 bg-white">
      <div className="border-b border-gray-200 p-4">
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
          <div className="min-w-0 flex-1">
            <h2 className="text-xl font-bold text-black">
              Rechnungen für {email}
            </h2>
            <p className="text-gray-600">
              {invoices.length} Rechnung(en) gefunden
            </p>
          </div>
          <div className="flex-shrink-0">
            <DownloadZipButton />
          </div>
        </div>
      </div>

      {/* Tabelle (deine bestehende Darstellung) */}
      <div className="hidden overflow-x-auto md:block">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">
                Rechnungsnummer
              </th>
              <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">
                Datum
              </th>
              <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">
                Betrag
              </th>
              <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">
                Status
              </th>
              <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">
                Zeitraum
              </th>
              <th className="px-4 py-3 text-center text-sm font-semibold text-gray-900">
                Download
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {invoices.map((invoice) => (
              <tr key={invoice.id} className="hover:bg-gray-50">
                <td className="px-4 py-3 text-sm text-gray-900">
                  {invoice.invoice_number || "Noch nicht vergeben"}
                </td>
                <td className="px-4 py-3 text-sm text-gray-900">
                  {formatDate(invoice.invoice_date)}
                </td>
                <td className="px-4 py-3 text-sm text-gray-900">
                  {formatCurrency(invoice.sum_gross, invoice.currency)}
                </td>
                <td
                  className={`px-4 py-3 text-sm font-medium ${getStatusColor(
                    invoice.status
                  )}`}
                >
                  {getStatusText(invoice.status)}
                </td>
                <td className="px-4 py-3 text-sm text-gray-900">
                  {invoice.service_period_from && invoice.service_period_to
                    ? `${formatDate(
                        invoice.service_period_from
                      )} - ${formatDate(invoice.service_period_to)}`
                    : "Nicht verfügbar"}
                </td>
                <td className="px-4 py-3 text-center">
                  {invoice.authorization_reference && (
                    <button
                      onClick={() => void downloadInvoiceViaShortId(invoice)}
                      className="inline-flex items-center gap-1 rounded bg-elm-700 px-3 py-1 text-sm text-white transition-colors hover:bg-elm-800"
                      title="PDF herunterladen"
                    >
                      <FaDownload size={12} />
                      PDF
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile-Karten */}
      <div className="space-y-4 p-4 md:hidden">
        {invoices.map((invoice) => (
          <div
            key={invoice.id}
            className="overflow-hidden rounded-lg border border-gray-200 p-4"
          >
            <div className="flex flex-wrap items-start gap-2">
              <div className="min-w-0 flex-1">
                <div className="text-sm text-gray-500">Rechnungsnummer</div>
                <div className="truncate font-semibold text-gray-900">
                  {invoice.invoice_number || "Noch nicht vergeben"}
                </div>
              </div>
              {invoice.authorization_reference && (
                <button
                  onClick={() => void downloadInvoiceViaShortId(invoice)}
                  className="inline-flex shrink-0 items-center gap-1 self-start whitespace-nowrap rounded bg-elm-700 px-3 py-1 text-sm text-white transition-colors hover:bg-elm-800"
                  title="PDF herunterladen"
                >
                  <FaDownload size={12} />
                  PDF
                </button>
              )}
            </div>

            <div className="mt-3 grid grid-cols-1 gap-2 text-sm sm:grid-cols-2">
              <div>
                <div className="text-gray-500">Datum</div>
                <div className="text-gray-900">
                  {formatDate(invoice.invoice_date)}
                </div>
              </div>
              <div>
                <div className="text-gray-500">Betrag</div>
                <div className="text-gray-900">
                  {formatCurrency(invoice.sum_gross, invoice.currency)}
                </div>
              </div>
              <div>
                <div className="text-gray-500">Status</div>
                <div
                  className={`font-medium ${getStatusColor(invoice.status)}`}
                >
                  {getStatusText(invoice.status)}
                </div>
              </div>
              <div>
                <div className="text-gray-500">Zeitraum</div>
                <div className="text-gray-900">
                  {invoice.service_period_from && invoice.service_period_to
                    ? `${formatDate(
                        invoice.service_period_from
                      )} - ${formatDate(invoice.service_period_to)}`
                    : "Nicht verfügbar"}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
