"use client";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import React, { useState } from "react";
import { FaSearch, FaSpinner } from "react-icons/fa";

interface FormValues {
  email: string;
}

export const InvoiceDownloadBatchForm = () => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isSubmitSuccessful },
  } = useForm<FormValues>();

  const [apiError, setApiError] = useState<string>("");
  const [requestedEmail, setRequestedEmail] = useState<string>("");

  const onSubmit: SubmitHandler<FormValues> = async ({ email }) => {
    setApiError("");
    setRequestedEmail(email);

    try {
      const res = await fetch("/api/invoice/requestLink", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email }),
      });
      // Immer "ok" (Enumeration vermeiden) – UI zeigt unten nur Erfolg/Info.
      if (!res.ok) {
        setApiError("Fehler beim Anfordern des Links");
      }
    } catch {
      setApiError("Fehler beim Anfordern des Links");
    }
  };

  return (
    <div className="mx-auto w-full max-w-6xl p-4">
      <div className="mb-6 rounded-lg border border-gray-600 bg-white p-6">
        {/* eslint-disable-next-line @typescript-eslint/no-misused-promises */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="flex flex-col md:flex-row md:items-end md:gap-4">
            <div className="flex-1">
              <label
                className="mb-2 block text-xl font-bold text-black"
                htmlFor="email"
              >
                E-Mail-Adresse:
              </label>
              <input
                placeholder="z.B. <EMAIL>"
                className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-700 focus:border-elm-600 focus:outline-none"
                id="email"
                type="email"
                {...register("email", {
                  required: "Dieses Feld ist erforderlich",
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: "Bitte geben Sie eine gültige E-Mail-Adresse ein",
                  },
                })}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.email.message}
                </p>
              )}
            </div>

            <button
              className="mt-2 flex items-center gap-2 rounded-md bg-elm-700 px-4 py-2 text-white transition-colors hover:bg-elm-800"
              type="submit"
              disabled={isSubmitting}
            >
              Link anfordern <FaSearch size={14} />
              {isSubmitting && <FaSpinner className="animate-spin" />}
            </button>
          </div>
        </form>
      </div>

      {/* Info-Box nach Submit */}
      {isSubmitSuccessful && (
        <div className="rounded-lg border border-gray-600 bg-white p-6">
          {apiError ? (
            <p className="text-red-600">{apiError}</p>
          ) : (
            <div>
              <h2 className="mb-2 text-xl font-bold text-black">
                E-Mail gesendet
              </h2>
              <p className="text-gray-700">
                Wenn es zu <b>{requestedEmail}</b> passende Rechnungen gibt,
                erhalten Sie in Kürze eine E-Mail mit einem Link zur Übersicht.
                Der Link ist 7 Tage gültig.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
