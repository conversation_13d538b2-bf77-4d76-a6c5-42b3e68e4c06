import "../styles/globals.css";
import { Mont<PERSON><PERSON>, Source_Sans_3 } from "next/font/google";

export const metadata = {
  title: "Ad Hoc by Eulektro",
  icons: {
    icon: "/favicon/favicon-32x32.png",
    apple: "favicon/favicon-32x32.png",
  },
  description: "Eulektro Ad Hoc Charging",
};

const montserrat = Montserrat({
  subsets: ["latin"],
  weight: ["400", "700"],
  variable: "--font-mont",
});

const sansPro = Source_Sans_3({
  subsets: ["latin"],
  weight: ["400", "700"],
  variable: "--font-sansPro",
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="de" className={` ${montserrat.variable} ${sansPro.variable}`}>
      {children}
    </html>
  );
}
