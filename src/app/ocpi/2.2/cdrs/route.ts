import { type NextRequest, NextResponse } from "next/server";

import { type Cdr, StatusEnum } from "../../../../../prisma/client";
import { z } from "zod";
import { InvoiceManager } from "~/utils/invoice";
import { prisma } from "~/server/db";
import {
  SendDebugMessage,
  SendErrorMessage,
  SendInfoMessage,
} from "~/utils/chat/sendMessage";
import { stripe } from "~/utils/stripe/stripe";
import { PrismaClientValidationError } from "@prisma/client/runtime/library";

export const revalidate = 0;

const CDRTokenSchema = z.object({
  uid: z.string(),
  type: z.enum(["RFID", "AD_HOC_USER"]),
  contract_id: z.string(),
});

const CoordinatesSchema = z.object({
  latitude: z.string(),
  longitude: z.string(),
});

const CDRLocationSchema = z
  .object({
    id: z.string(),
    name: z.string(),
    address: z.string(),
    city: z.string(),
    postal_code: z.string(),
    country: z.string(),
    coordinates: CoordinatesSchema,
    evse_uid: z.string(),
    evse_id: z.string(),
    connector_id: z.string(),
    connector_standard: z.string(),
    connector_format: z.enum(["SOCKET", "CABLE"]),
    connector_power_type: z.string(),
  })
  .nonstrict();

const TariffAltTextSchema = z.object({
  language: z.string(),
  text: z.string(),
});

const PriceComponentSchema = z.object({
  type: z.enum(["FLAT", "ENERGY"]),
  price: z.number(),
  step_size: z.number(),
});

const TariffElementSchema = z.object({
  price_components: z.array(PriceComponentSchema),
});

const TariffSchema = z.object({
  country_code: z.string(),
  party_id: z.string(),
  id: z.string(),
  currency: z.string(),
  type: z.enum(["REGULAR"]),
  tariff_alt_text: z.array(TariffAltTextSchema),
  elements: z.array(TariffElementSchema),
  last_updated: z.string().refine((value) => !isNaN(Date.parse(value)), {
    message: "Invalid date format",
  }),
});

const ChargingPeriodDimensionSchema = z.object({
  type: z.enum(["ENERGY", "PARKING_TIME", "TIME"]),
  volume: z.number(),
});

const ChargingPeriodSchema = z.object({
  start_date_time: z.string().refine((value) => !isNaN(Date.parse(value)), {
    message: "Invalid date format",
  }),
  dimensions: z.array(ChargingPeriodDimensionSchema),
  tariff_id: z.string().optional().default(""),
});

const TotalCostSchema = z.object({
  excl_vat: z.number(),
});

const CDRSchema = z
  .object({
    id: z.string(),
    country_code: z.string(),
    party_id: z.string(),
    start_date_time: z.string().refine((value) => !isNaN(Date.parse(value)), {
      message: "Invalid date format",
    }),
    end_date_time: z.string().refine((value) => !isNaN(Date.parse(value)), {
      message: "Invalid date format",
    }),
    session_id: z.string(),
    cdr_token: CDRTokenSchema,
    auth_method: z.enum(["COMMAND", "AUTH_REQUEST"]),
    cdr_location: CDRLocationSchema,
    currency: z.string(),
    tariffs: z.array(TariffSchema).optional().default([]),
    charging_periods: z.array(ChargingPeriodSchema),
    total_cost: TotalCostSchema,
    total_fixed_cost: TotalCostSchema,
    total_energy: z.number(),
    total_energy_cost: TotalCostSchema,
    total_time: z.number(),
    total_time_cost: TotalCostSchema,
    total_parking_time: z.number(),
    total_parking_cost: TotalCostSchema,
    credit: z.boolean(),
    last_updated: z.string().refine((value) => !isNaN(Date.parse(value)), {
      message: "Invalid date format",
    }),
    authorization_reference: z.string(),
  })
  .nonstrict();

const checkCdrExists = async (cdr: Cdr) => {
  return prisma.cdr.findUnique({
    where: {
      id: cdr.id,
    },
  });
};

export async function POST(request: NextRequest) {
  const cdr = CDRSchema.safeParse(await request.json());
  if (!cdr.success) {
    void (await SendInfoMessage("POST api/ocpi/2.2/cdrs CDR validation Error", {
      zodError: cdr.error,
    }));
    return NextResponse.json({
      status_code: 3000,
      status_message: "CDR validation Error",
      timestamp: new Date().toISOString(),
    });
  }

  if (await checkCdrExists(cdr.data)) {
    void (await SendInfoMessage(
      `OCPI CDR POST: CDR with id ${cdr.data.id} already exists. Status 1000 (ok)returned anyway.`
    ));
    return NextResponse.json({
      status_code: 1000,
      status_message: "Cdr already exists",
      timestamp: new Date().toISOString(),
    });
  }

  const createdCdr = await prisma.cdr.create({
    data: cdr.data,
  });

  if (!createdCdr) {
    void (await SendInfoMessage(
      `OCPI CDR POST create error, could not create cdr with id ${cdr.data.id}`
    ));
    return NextResponse.json({
      status_code: 3000,
      status_message: "could not store cdr",
      timestamp: new Date().toISOString(),
    });
  }

  let paymentIntent = await prisma.paymentIntent.findUnique({
    where: {
      authorization_reference: cdr.data.authorization_reference,
    },
  });

  if (!paymentIntent) {
    void (await SendInfoMessage(
      `OCPI CDR POST:  Could not find paymentIntent with authorization_reference ${cdr.data.authorization_reference}`
    ));
    return NextResponse.json({
      status_code: 3000,
      status_message: "Could not find paymentIntent",
      timestamp: new Date().toISOString(),
    });
  }

  if (paymentIntent.status != StatusEnum.SESSION_RECEIVED) {
    void (await SendInfoMessage(
      `OCPI CDR POST create error, paymentIntent with authorization_reference ${cdr.data.authorization_reference} has wrong status ${paymentIntent.status}`
    ));
  }

  const invoiceManager = new InvoiceManager();

  const invoice = await invoiceManager.createInvoice({
    subject: `Ladevorgang am Ladepunkt ${cdr.data.cdr_location.evse_id} am Standort ${cdr.data.cdr_location.address}, ${cdr.data.cdr_location.postal_code} ${cdr.data.cdr_location.city}`,
    emp_country_code: "DE",
    emp_party_id: "EUL",
    mailToSend: paymentIntent.invoiceMail || undefined,
    service_period_from: new Date(cdr.data.start_date_time),
    service_period_to: new Date(cdr.data.end_date_time),
    local_start_datetime:
      new Date(cdr.data.start_date_time).toLocaleString("de-DE") || "",
    local_end_datetime:
      new Date(cdr.data.end_date_time).toLocaleString("de-DE") || "",
    authorization_reference: cdr.data.authorization_reference,
    metadata: {
      paymentIntent: paymentIntent.id,
    },
  });

  if (!invoice) {
    void (await SendInfoMessage(
      `OCPI CDR POST create error, could not create invoice for cdr ${cdr.data.id}`
    ));
    return NextResponse.json({
      status_code: 3000,
      status_message: "Could not create invoice",
      timestamp: new Date().toISOString(),
    });
  }

  //there is always a kWh invoice item
  await invoiceManager.addInvoiceItem(invoice.id, {
    title: "Preis für geladene kWh",
    unit: "kWh",
    unit_price_gross: paymentIntent.energy_price,
    amount: cdr.data.total_energy,
    tax_rate: paymentIntent.tax_rate,
    description: "Kosten für geladene kWh",
  });

  // if there is a session fee, add to invoice items
  if (paymentIntent?.session_fee && paymentIntent.session_fee > 0) {
    await invoiceManager.addInvoiceItem(invoice.id, {
      title: "Grundpreis",
      unit: "Sitzung",
      unit_price_gross: paymentIntent.session_fee,
      amount: 1,
      tax_rate: paymentIntent.tax_rate,
      description: "Startgebühr",
    });
  }
  // if there are blocking fees, add invoice item
  if (paymentIntent?.blocking_fee && paymentIntent.blocking_fee > 0) {
    const minutes = cdr.data.total_time * 60;
    let blocking_minutes = minutes - paymentIntent.blocking_fee_start;
    if (blocking_minutes > 0) {
      const blocking_costs = blocking_minutes * paymentIntent.blocking_fee;
      if (
        paymentIntent.blocking_fee_max > 0 &&
        blocking_costs > paymentIntent.blocking_fee_max
      ) {
        //max minutes needs to be calculated, because only max price is in DB
        blocking_minutes =
          paymentIntent.blocking_fee_max / paymentIntent.blocking_fee;
      }

      const max_block_text = paymentIntent.blocking_fee_max
        ? ` ab ${
            paymentIntent.blocking_fee_start
          } Minuten (max. ${paymentIntent.blocking_fee_max.toLocaleString(
            "de",
            {
              style: "currency",
              currency: "EUR",
            }
          )})`
        : "";
      await invoiceManager.addInvoiceItem(invoice.id, {
        title: `Blockiergebühr${max_block_text}`,
        unit: "Minute",
        unit_price_gross: paymentIntent.blocking_fee,
        amount: blocking_minutes,
        tax_rate: paymentIntent.tax_rate,
        description: `Blockiergebühr${max_block_text}`,
      });
    }
  }

  const invoiceWithPositions = await prisma.invoice.findUnique({
    where: {
      id: invoice.id,
    },
  });

  if (!invoiceWithPositions || !invoiceWithPositions.sum_gross) {
    void (await SendErrorMessage(
      `OCPI CDR POST create error, invoice has no sum_gross for cdr: ${cdr.data.id}`
    ));
    return NextResponse.json({
      status_code: 3000,
      status_message: "Could not find invoice sum_gross",
      timestamp: new Date().toISOString(),
    });
  }

  let amount_to_capture_in_cent = Math.round(
    invoiceWithPositions.sum_gross * 100
  );

  if (amount_to_capture_in_cent > paymentIntent.amount_capturable) {
    void (await SendErrorMessage(
      `OCPI CDR POST create error, amount to capture ${
        amount_to_capture_in_cent / 100
      } is bigger than amount capturable ${
        paymentIntent.amount_capturable / 100
      } 😱 💸`
    ));
    amount_to_capture_in_cent = paymentIntent.amount_capturable;
  }
  let notBillable = false;
  let paymentStatus: StatusEnum = StatusEnum.REFUNDED;
  // check if total energy and total time is bigger than min_kwh_in_kwh and min_time_in_min from the emp settings
  if (
    cdr.data.total_energy >= paymentIntent.min_kwh_in_kwh &&
    Math.floor(cdr.data.total_time) * 60 + (cdr.data.total_time % 1) * 60 >=
      paymentIntent.min_time_in_min
  ) {
    const res = await stripe.paymentIntents.capture(paymentIntent.id, {
      amount_to_capture: amount_to_capture_in_cent,
      metadata: {
        cdr_id: cdr.data.id,
        start: cdr.data.start_date_time,
        end: cdr.data.end_date_time,
        total_energy: cdr.data.total_energy,
        evse: cdr.data.cdr_location.evse_id,
        address: cdr.data.cdr_location.address,
      },
      statement_descriptor_suffix: `${cdr.data.id.slice(-11)}X`,
    });

    if (res.status == "succeeded") {
      void (await SendInfoMessage(
        `${(amount_to_capture_in_cent / 100).toLocaleString("de-DE", {
          style: "currency",
          currency: "EUR",
        })} € 💶 💰\nSessonId: ${cdr.data.id}\nCharger: ${
          cdr.data.cdr_location.evse_id
        } / ${cdr.data.cdr_location.address}`
      ));
    }

    paymentStatus =
      res.status == "succeeded"
        ? StatusEnum.CAPTURED
        : StatusEnum.CAPTURE_FAILED;
    if (paymentStatus == StatusEnum.CAPTURE_FAILED) {
      void (await SendInfoMessage(
        `Payment failed for CDR ${cdr.data.id} and paymentIntent ${paymentIntent.id}`
      ));
    }
  } else {
    if (cdr.data.total_energy < 0) {
      await stripe.paymentIntents.update(paymentIntent.id, {
        metadata: {
          meterBug: `Volume = ${cdr.data.total_energy}`,
        },
      });
      await SendInfoMessage(
        `Payment Intent not cancled, because total energy is negativ. Please check! cdr=${cdr.data.id} total_energy=${cdr.data.total_energy}`
      );
    } else {
      await stripe.paymentIntents.update(paymentIntent.id, {
        metadata: {
          cancelBy: "cdr create",
        },
      });
      notBillable = true;

      await stripe.paymentIntents.cancel(paymentIntent.id);
      void (await SendInfoMessage(
        `POST ocpi/2.2/cdrs Payment for CDR ${cdr.data.id} and paymentIntent ${paymentIntent.id} 
      was refunded because total_energy ${cdr.data.total_energy} kWh or total_time ${cdr.data.total_time} min 
      was smaller than min_kwh_in_kwh ${paymentIntent.min_kwh_in_kwh} kWh or min_time_in_min ${paymentIntent.min_time_in_min} min`
      ));
    }
  }
  try {
    paymentIntent = await prisma.paymentIntent.update({
      where: {
        id: paymentIntent.id,
      },
      data: {
        status: paymentStatus,
        amount_received:
          cdr.data.total_energy < 0 ? 0 : amount_to_capture_in_cent,
        cdr: {
          connect: {
            id: cdr.data.id,
          },
        },
        location: {
          connect: {
            id: cdr.data.cdr_location.id,
          },
        },
        session: {
          connect: {
            id: cdr.data.session_id,
          },
        },
      },
    });
  } catch (e) {
    if (e instanceof PrismaClientValidationError) {
      void (await SendInfoMessage(
        `Prisma Error: Error updating payment intent ${e.message} ${
          e?.stack ?? ""
        }`
      ));
    } else {
      void (await SendInfoMessage(
        `Prisma Error: Error updating payment intent sessionId:${cdr.data.session_id} (Session missing?)`
      ));
    }
  }

  if (paymentStatus == StatusEnum.CAPTURED) {
    // generate invoiceNumber and create invoice pdf
    await invoiceManager.finalize(invoice.id);
    await invoiceManager.setStatusToPaid(invoice.id);
    void (await SendDebugMessage(`Invoice for CDR generated ${cdr.data.id}`));

    if (paymentIntent.invoiceMail) {
      await invoiceManager.send(invoice.id, paymentIntent.id);
      void (await SendDebugMessage(`Invoice sent for CDR ${cdr.data.id}`));
    }
  } else if (paymentStatus == StatusEnum.CAPTURE_FAILED) {
    void (await SendErrorMessage(
      `Capture failed response for CDR ${cdr.data.id}`
    ));
  } else {
    if (!notBillable) {
      // nur wenn notBillable nicht gesetzt ist ist es ein fehler
      void (await SendInfoMessage(
        `can't finalize invoice (${invoice.id} because payment failed we have to investigate this manually`
      ));
    }
  }

  return NextResponse.json({
    data: "ok",
    status_code: 1000,
    status_message: "Success",
    timestamp: new Date().toISOString(),
  });
}
