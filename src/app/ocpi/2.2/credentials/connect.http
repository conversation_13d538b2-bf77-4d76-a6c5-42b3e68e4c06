POST https://beta.ocpi.longship.io/ocpi/2.2.1/credentials
Authorization: Token ac443ef477270d3e89874ec0beb0265c # Longship inintal Token
Content-Type: application/json

{
  "url": "https://jk.adhoc.eulektro.de/ocpi/versions/",
  "token": "M2VmMTg1NTA2Mzk1NDVmMzliOWU0ODQzMTYxMjhiYmI=", // our base64 "send secret""
  "roles": [{
    "role": "EMSP",
    "party_id": "XXX",
    "country_code": "DE",
    "business_details": {
      "name": "Test Company"
    }
  }]
}

receiving secret => 56b1cbcef88c98647ad5d1fba5e88d50

###

GET http://beta.ocpi.longship.io/ocpi/2.2/locations
Authorization: Token NTZiMWNiY2VmODhjOTg2NDdhZDVkMWZiYTVlODhkNTA=
Content-Type: application/json


###

#########
GET https://beta.ocpi.longship.io/ocpi/2.2/sessions/?date_from=2024-01-02T12:00:00&limit=5
Authorization: Token 56b1cbcef88c98647ad5d1fba5e88d50
Content-Type: application/json
