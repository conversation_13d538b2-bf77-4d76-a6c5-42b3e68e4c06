import { type NextRequest, NextResponse } from "next/server";
import { env } from "~/env.mjs";
import { z } from "zod";
import { prisma } from "~/server/db";
export const revalidate = 0;

const credentialSchema = z.object({
  token: z.string(),
  url: z.string(),
  role: z.string(),
  business_details: z.string(),
  party_id: z.string(),
  country_code: z.string(),
});

export async function POST(request: NextRequest) {
  const credential = credentialSchema.safeParse(await request.json());

  if (!credential.success) {
    return NextResponse.json({
      status: 3000,
      statusText: "Bad Request",
      timestamp: new Date().toISOString(),
    });
  }

  const OCPI_DOMAIN = env.OCPI_DOMAIN;

  const responseData = {
    token: "9b3c05f2c47d50bdac8f4f2b4dde002h", // Dein Authentifizierungstoken für die andere Partei
    url: `${OCPI_DOMAIN}/ocpi/2.2`, // Ersetze dies durch die URL deines eMSP-Server-Endpunkts
    role: "EMSP",
    business_details: {
      name: "Eulektro",
      website: "https://eulektro.de",
    },
    party_id: "EUL", // Deine eMSP-Party-ID
    country_code: "DE", // Dein eMSP-Ländercode
  };

  void (await prisma.ocpiConnection.create({
    data: {
      name: "Eulektro", // ToDo get name from ???
      ...credential.data,
    },
  }));

  return NextResponse.json({
    data: responseData,
    status_code: 1000,
    status_message: "Success",
    timestamp: new Date().toISOString(),
  });
}
