import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import {
  ConnectorSchema,
  EvseSchema,
  LocationSchema,
} from "~/utils/schema/location";
import { prisma } from "~/server/db";
export const revalidate = 0;
const now = new Date().toISOString();
const UpdateEvseOfLocation = async (
  data: z.infer<typeof EvseSchema>,
  locationId: string,
  evseId: string
) => {
  const locationToUpdate = await prisma.location.findUnique({
    where: { id: locationId },
  });

  if (!locationToUpdate) {
    return NextResponse.json({
      status_code: 3000,
      status_message: "Location not found",
      timestamp: new Date().toISOString(),
    });
  }

  const existingEvseIndex = locationToUpdate.evses.findIndex(
    (e) => e.uid === evseId
  );

  if (existingEvseIndex !== -1) {
    locationToUpdate.evses[existingEvseIndex] = {
      ...locationToUpdate.evses[existingEvseIndex],
      ...data,
    };
  } else {
    locationToUpdate.evses.push(data);
  }

  await prisma.location.update({
    where: { id: locationId },
    data: {
      evses: locationToUpdate.evses,
      last_updated: now,
    },
  });
};

export async function PUT(
  request: NextRequest,
  params: { params: { slug: string[] } }
) {
  // const countryCode = z.string().min(2).safeParse(params.params.slug[0]);
  // const partyId = z.string().min(3).safeParse(params.params.slug[1]);
  const locationId = z.string().min(1).max(36).safeParse(params.params.slug[2]);
  const evseId = z.string().min(1).max(40).safeParse(params.params.slug[3]);
  const connectorId = z.string().min(1).max(3).safeParse(params.params.slug[4]);

  const data = z
    .union([LocationSchema, EvseSchema, ConnectorSchema])
    .safeParse(await request.json());

  if (!data.success || !locationId.success) {
    return NextResponse.json({
      status_code: 3000,
      status_message: "Data schema validation failed",
      timestamp: new Date().toISOString(),
    });
  }

  const now = new Date().toISOString();

  const LocationSchemaValidation = LocationSchema.safeParse(data.data);
  const EvseSchemaValidation = EvseSchema.safeParse(data.data);
  const ConnectorSchemaValidation = ConnectorSchema.safeParse(data.data);

  switch (true) {
    case LocationSchemaValidation.success:
      if (!LocationSchemaValidation.success) {
        break;
      }
      const location = {
        ...LocationSchemaValidation.data,
        last_updated: now,
      };
      if (location.id) {
        const { id: _, ...locationWithoutId } = location;
        await prisma.location.upsert({
          where: { id: locationId.data },
          update: locationWithoutId,
          create: location,
        });
      }
      break;

    case EvseSchemaValidation.success:
      if (!evseId.success) {
        break;
      }
      if (!EvseSchemaValidation.success) {
        break;
      }
      void (await UpdateEvseOfLocation(
        EvseSchemaValidation.data,
        locationId.data,
        evseId.data
      ));
      break;

    case ConnectorSchemaValidation.success:
      if (
        !evseId.success ||
        !connectorId.success ||
        !ConnectorSchemaValidation.success
      ) {
        break;
      }
      const newConnector = {
        ...ConnectorSchemaValidation.data,
        last_updated: now,
      };
      const locationToUpdateConnector = await prisma.location.findUnique({
        where: {
          id: locationId.data,
        },
      });

      if (!locationToUpdateConnector) {
        return NextResponse.json({
          status_code: 3000,
          status_message: "Data schema validation failed, location not found",
          timestamp: new Date().toISOString(),
        });
      }

      const existingEvseIndexConnector: number =
        locationToUpdateConnector.evses.findIndex((e) => e.uid === evseId.data);

      const evse = locationToUpdateConnector.evses[existingEvseIndexConnector];
      if (!evse || !evse.connectors) {
        return NextResponse.json({
          status_code: 3000,
          status_message:
            "Data schema validation failed, evse not found or no connectors",
          timestamp: new Date().toISOString(),
        });
      }

      const existingConnectorIndex = evse.connectors.findIndex(
        (e) => e.id === connectorId.data
      );

      const existingEvse =
        locationToUpdateConnector.evses[existingEvseIndexConnector];
      if (!existingEvse) {
        return NextResponse.json({
          status_code: 3000,
          status_message: "Data schema validation failed, evse not found",
          timestamp: new Date().toISOString(),
        });
      }

      if (existingConnectorIndex !== -1) {
        existingEvse.connectors[existingConnectorIndex] = newConnector;
      } else {
        existingEvse.connectors.push(newConnector);
      }

      await prisma.location.update({
        where: { id: locationId.data },
        data: {
          evses: locationToUpdateConnector.evses,
          last_updated: now,
        },
      });

      break;

    default:
      return NextResponse.json({
        status_code: 3000,
        status_message: "Data schema validation failed",
        timestamp: new Date().toISOString(),
      });
  }

  return NextResponse.json({
    data: "ok",
    status_code: 1000,
    status_message: "Success",
    timestamp: new Date().toISOString(),
  });
}

const PatchBodySchema = z
  .object({
    status: z.string().optional(),
    name: z.string().optional(),
    last_updated: z.string(),
  })
  .refine((obj) => "status" in obj || "name" in obj, {
    message: "At least one of the fields must be present",
  });

export async function PATCH(
  request: NextRequest,
  params: { params: { slug: string[] } }
) {
  // const countryCode = z.string().min(2).safeParse(params.params.slug[0]);
  // const partyId = z.string().min(3).safeParse(params.params.slug[1]);
  const locationId = z.string().min(1).max(36).safeParse(params.params.slug[2]);

  const evse_uid = z.string().min(10).max(36).safeParse(params.params.slug[3]);

  const locationUpdateData = PatchBodySchema.safeParse(await request.json());

  if (locationUpdateData.success && locationId.success && evse_uid.success) {
    // Remove last_updated key from the data object, as it should not be updated in the database
    const data = locationUpdateData.data;

    const locationToUpdate = await prisma.location.findUnique({
      where: { id: locationId.data },
    });

    if (!locationToUpdate) {
      return NextResponse.json({
        status_code: 3000,
        status_message: `Error updating location, location not found`,
        timestamp: new Date().toISOString(),
      });
    }

    const existingEvseIndex = locationToUpdate.evses.findIndex(
      (e) => e.uid === evse_uid.data
    );

    if (existingEvseIndex !== -1) {
      const oldEveseData = locationToUpdate.evses[existingEvseIndex];
      if (!oldEveseData) {
        return NextResponse.json({
          status_code: 3000,
          status_message: `Error updating location, evse not found`,
          timestamp: new Date().toISOString(),
        });
      }

      locationToUpdate.evses[existingEvseIndex] = {
        ...oldEveseData,
        ...locationUpdateData.data,
      };
      const { id: _, ...locationWithoutId } = locationToUpdate;
      try {
        const res = await prisma.location.update({
          where: { id: locationId.data },
          data: locationWithoutId,
        });
      } catch (e) {
        return NextResponse.json({
          status_code: 3000,
          status_message: `Error updating location`,
          timestamp: new Date().toISOString(),
        });
      }
    } else {
      return NextResponse.json({
        status_code: 3000,
        status_message: "Error updating location, evse not found",
        timestamp: new Date().toISOString(),
      });
    }

    return NextResponse.json({
      status_code: 1000,
      status_message: "Success",
      timestamp: new Date().toISOString(),
    });
  }
  return NextResponse.json({
    status_code: 3000,
    status_message: "Error updating location, schema validation failed",
    timestamp: new Date().toISOString(),
  });
}
