### Beta
GET https://beta.ocpi.longship.io/ocpi/2.2/locations/
Authorization: Token 480da6548bfd4a0380fe6cf7800e6c02

### Prod
GET https://ocpi.longship.io/ocpi/2.2/locations/
Authorization: Token c856cd241bd447288f9b840f25eab0a2

###
PUT https://beta.adhoc.eulektro.de/ocpi/2.2/locations/DE/EUL/DEEULS000X
Content-Type: application/json

{
      "id": "DEEULS000X",
      "country_code": "DE",
      "party_id": "EUL",
      "publish": true,
      "publish_allowed_to": [],
      "name": "HQKeller",
      "address": "Werderstraße 69",
      "city": "Bremen",
      "postal_code": "28199",
      "country": "DEU",
      "coordinates": {
        "latitude": "53.070470",
        "longitude": "8.807160"
      },
      "parking_type": "ON_DRIVEWAY",
      "evses": [
        {
          "uid": "b350f509-762f-4288-b4a7-5a2247f63d71",
          "evse_id": "DE*EUL*E000X*01",
          "status": "AVAILABLE",
          "capabilities": [
            "REMOTE_START_STOP_CAPABLE",
            "UNLOCK_CAPABLE"
          ],
          "connectors": [
            {
              "id": "1",
              "standard": "IEC_62196_T2",
              "format": "SOCKET",
              "power_type": "AC_3_PHASE",
              "max_voltage": 230,
              "max_amperage": 16,
              "max_electric_power": 11000,
              "tariff_ids": [
                "DEEULTDEFAULT"
              ],
              "last_updated": "2023-04-26T16:09:09.467Z"
            }
          ],
          "coordinates": {
            "latitude": "53.070470",
            "longitude": "8.807160"
          },
          "physical_reference": "DEEULS000X-1",
          "last_updated": "2023-04-27T14:02:40.000Z"
        },
        {
          "uid": "c526986d-5dbd-473a-8c05-a51b8492d728",
          "evse_id": "DE*EUL*E000X*02",
          "status": "AVAILABLE",
          "capabilities": [
            "REMOTE_START_STOP_CAPABLE",
            "UNLOCK_CAPABLE"
          ],
          "connectors": [
            {
              "id": "2",
              "standard": "IEC_62196_T2",
              "format": "SOCKET",
              "power_type": "AC_3_PHASE",
              "max_voltage": 230,
              "max_amperage": 16,
              "max_electric_power": 11000,
              "tariff_ids": [
                "DEEULTDEFAULT"
              ],
              "last_updated": "2023-04-26T16:09:09.467Z"
            }
          ],
          "coordinates": {
            "latitude": "53.070470",
            "longitude": "8.807160"
          },
          "physical_reference": "DEEULS000X-2",
          "last_updated": "2023-04-27T13:09:28.000Z"
        }
      ],
      "operator": {
        "name": "Eulektro"
      },
      "owner": {
        "name": "Eulektro Owner"
      },
      "time_zone": "Europe/Berlin",
      "charging_when_closed": true,
      "last_updated": "2023-04-27T14:02:40.000Z"
}

### add or update evse
PUT https://beta.adhoc.eulektro.de/ocpi/2.2/locations/DE/EUL/DEEULS000X
Content-Type: application/json

{
  "uid": "b350f509-762f-4288-b4a7-5a2247f63d74",
  "evse_id": "DE*EUL*E000X*01",
  "status": "AVAILABLE",
  "capabilities": [
    "REMOTE_START_STOP_CAPABLE",
    "UNLOCK_CAPABLE"
  ],
  "connectors": [
    {
      "id": "1",
      "standard": "IEC_62196_T2",
      "format": "SOCKET",
      "power_type": "AC_3_PHASE",
      "max_voltage": 230,
      "max_amperage": 16,
      "max_electric_power": 11000,
      "tariff_ids": [
        "DEEULTDEFAULT"
      ],
      "last_updated": "2023-04-26T16:09:09.467Z"
    }
  ],
  "coordinates": {
    "latitude": "53.070470",
    "longitude": "8.807160"
  },
  "physical_reference": "DEEULS000X-1",
  "last_updated": "2023-04-27T14:02:40.000Z"
}

### add or update connector
PUT https://beta.adhoc.eulektro.de/ocpi/2.2/locations/DE/EUL/DEEULS000X/b350f509-762f-4288-b4a7-5a2247f63d71/1
Content-Type: application/json

{
  "id": "1",
  "standard": "IEC_62196_T2",
  "format": "SOCKET",
  "power_type": "AC_3_PHASE",
  "max_voltage": 230,
  "max_amperage": 17,
  "max_electric_power": 11000,
  "tariff_ids": [
    "DEEULTDEFAULT"
  ],
  "last_updated": "2023-04-26T16:09:09.467Z"
}


###

PATCH http://localhost:3000/ocpi/2.2/locations/DE/EUL/DEEULS000X/726a66d4-df2a-415b-86b0-67948e85befa
Authorization: Token 9b3c05f2c47d60bdac8f4f2b4dde0bbb
content-type: application/json

{
  "statu": "METER",
  "last_updated": "2023-05-24T18:31:39.262Z"
}
