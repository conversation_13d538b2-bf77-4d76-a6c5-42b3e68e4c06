import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";

import { prisma } from "~/server/db";

import { env } from "~/env.mjs";

import {
  SendDebugMessage,
  SendErrorMessage,
  SendInfoMessage,
} from "~/utils/chat/sendMessage";
import {
  Prisma,
  type Session,
  StatusEnum,
} from "../../../../../../prisma/client";

import {
  fetchSessionFromBackend,
  getEvseIdByEvseUid,
} from "~/utils/data/getBy";
import SessionUpdateInput = Prisma.SessionUpdateInput;
import { sendError } from "next/dist/server/api-utils";

export const revalidate = 0;
const chargingPeriodSchema = z.object({
  last_updated: z.string(),
  end_date_time: z.string().optional(),
  status: z.string().optional(),
  kwh: z.number(),
  total_cost: z.object({
    excl_vat: z.number(),
  }),
  charging_periods: z.array(
    z.object({
      start_date_time: z.string(),
      tariff_id: z.string().optional().default(""),
      dimensions: z.array(
        z.object({
          type: z.string(),
          volume: z.number(),
        })
      ),
    })
  ),
});

export async function PATCH(
  request: NextRequest,
  params: { params: { slug: string[] } }
) {
  // const country_code = z.string().min(2).safeParse(params.params.slug[0]);
  // const party_id = z.string().min(3).safeParse(params.params.slug[1]);
  const session_id = z
    .string()
    .min(36)
    .max(36)
    .safeParse(params.params.slug[2]);

  const newSessionData = chargingPeriodSchema.safeParse(await request.json());

  if (!session_id.success) {
    await SendErrorMessage(
      `api/ocpi/2.2/sessions/[slug] No session id provided`
    );
    return NextResponse.json({
      status_code: 3000,
      status_message: "No session id provided",
      timestamp: new Date().toISOString(),
    });
  }

  if (!newSessionData.success) {
    await SendErrorMessage(
      `api/ocpi/2.2/sessions/[slug] invalid session Schema`,
      { zodError: newSessionData.error }
    );
    return NextResponse.json({
      status_code: 3000,
      status_message: "No session data provided",
      timestamp: new Date().toISOString(),
    });
  }

  const currentSession = await prisma.session.findUnique({
    where: { id: session_id.data },
  });
  let status_code = 1000;
  let status_message = "Success";
  if (newSessionData.success && currentSession) {
    const updatedChargingPeriods = [
      ...currentSession.charging_periods,
      ...newSessionData.data.charging_periods,
    ];

    void (await prisma.session.update({
      where: { id: session_id.data },
      data: {
        charging_periods: updatedChargingPeriods,
        kwh: newSessionData.data.kwh,
        total_cost: newSessionData.data.total_cost,
        last_updated: newSessionData.data.last_updated,
        end_date_time:
          newSessionData.data.end_date_time || currentSession?.end_date_time,
        status: newSessionData.data.status || currentSession?.status,
      },
    }));
  } else if (session_id.success) {
    // Get all sessions from the last 24h and search for the id == session_id.data
    const currentDate = new Date();
    const dateFrom = new Date(currentDate);
    currentDate.setDate(currentDate.getDate() + 1);
    dateFrom.setDate(dateFrom.getDate() - 1);

    const sessionUrl = `${
      env.LONGSHIP_DOMAIN
    }/ocpi/2.2/sessions?date_from=${dateFrom.toISOString()}&date_to=${currentDate.toISOString()}`;

    try {
      await SendInfoMessage(
        `Received PATCH session but no session was created before. Try to find session and create sessionID: ${session_id.data}`
      );
      const session = await fetchSessionFromBackend(
        sessionUrl,
        session_id.data
      );

      if (session) {
        try {
          void (await prisma.session.create({
            data: session,
          }));
          try {
            const res = await prisma.paymentIntent.update({
              where: {
                authorization_reference: session.authorization_reference,
              },
              data: {
                status: StatusEnum.SESSION_RECEIVED,
              },
            });
            if (!res) {
              await SendErrorMessage(
                `Manually adding session: ${session_id.data} failed auth_ref:${session.authorization_reference}`
              );
              throw new Error(
                "PATCH ocpi/2.2/sessions/[...slug] PaymentIntent not found in DB"
              );
            }
            await SendDebugMessage(
              `Manually adding session ${session_id.data} successfull`
            );
          } catch (error) {
            await SendInfoMessage(
              `PaymentIntent for authorization_reference ${session.authorization_reference} not found`
            );
          }
        } catch (error) {
          await SendErrorMessage(
            `Catch block session PATCH ${session_id.data} `
          );
        }
      } else {
        status_message = "Session not found in CPO endpoint";
        status_code = 3000;
      }
    } catch (error) {
      if (error instanceof Error) {
        await SendErrorMessage(
          `Error in PATCH Session: ${error.message ?? "No exception message"}`
        );
      }

      status_message = "Error fetching sessions from CPO endpoint";
      status_code = 3000;
    }
  }
  return NextResponse.json({
    status_code: status_code,
    status_message: status_message,
    timestamp: new Date().toISOString(),
  });
}

const ValidatePutSessionPayload = z
  .object({
    id: z.string(),
    authorization_reference: z.string(),
    location_id: z.string(),
    connector_id: z.string(),
    status: z.string(),
    evse_uid: z.string(),
  })
  .nonstrict();

export async function PUT(request: NextRequest) {
  // const country_code = z.string().min(2).safeParse(params.params.slug[0]);
  // const party_id = z.string().min(3).safeParse(params.params.slug[1]);
  // const session_id = z
  //   .string()
  //   .min(36)
  //   .max(36)
  //   .safeParse(params.params.slug[2]);

  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const payload = ValidatePutSessionPayload.safeParse(await request.json());

  if (!payload.success) {
    void (await SendInfoMessage(
      "api/ocpi/2.2/sessions/[slug] Error validating payload",
      {
        zodError: payload.error,
      }
    ));
    return NextResponse.json({
      status_code: 3000,
      status_message: "Error validating payload",
      timestamp: new Date().toISOString(),
    });
  }

  const { id, ...dataWithoutId } = payload.data;
  await SendDebugMessage(
    `Session (initial) PUT for:\n authorization_reference:${payload.data.authorization_reference}\nLocation: ${payload.data.location_id}\n}`
  );

  void (await prisma.session.upsert({
    where: {
      id: id,
    },
    update: dataWithoutId as SessionUpdateInput,
    create: payload.data as Session,
  }));

  const paymentIntent = await prisma.paymentIntent.findUnique({
    where: {
      authorization_reference: payload.data.authorization_reference,
    },
  });

  if (!paymentIntent) {
    await SendErrorMessage(
      `Session PUT error for authref: ${payload.data.authorization_reference} sessionId: ${id}`
    );
    return NextResponse.json({
      status_code: 3000,
      status_message: " No paymentIntent found for this session",
      timestamp: new Date().toISOString(),
    });
  }
  if (
    [
      StatusEnum.CREATED,
      StatusEnum.PAYMENT_GUARANTED,
      StatusEnum.START_REQUESTED,
      StatusEnum.START_ACCEPTED,
      StatusEnum.START_REJECTED,
    ].some((x) => x === paymentIntent.status)
  ) {
    const updatedPaymentItent = await prisma.paymentIntent.update({
      where: { authorization_reference: payload.data.authorization_reference },
      data: {
        status: StatusEnum.SESSION_RECEIVED,
      },
    });
    if (!updatedPaymentItent) {
      await SendInfoMessage(
        `Error received session and can't find paymentIntent for the sesseion ${payload.data.authorization_reference}`
      );
    } else {
      let evseId: string | undefined | null = "";
      try {
        evseId = await getEvseIdByEvseUid(payload.data.evse_uid);
      } catch (e) {}
      await SendDebugMessage(
        `Session Received for:\n authorization_reference:${
          payload.data.authorization_reference
        }\nLocation: ${payload.data.location_id}\nEvseId: ${evseId ?? ""}`
      );
    }
  }

  return NextResponse.json({
    status_code: 1000,
    status_message: "Success",
    timestamp: new Date().toISOString(),
  });
}
