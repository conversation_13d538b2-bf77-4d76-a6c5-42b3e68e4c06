PATCH http://localhost:3000/ocpi/2.2/sessions/DE/EUL/DEEULB1D5722D83C7494FA220ABBA4DF0D50
Authorization: Token e5d5698267304c43b30c18379695eda0
Content-Type: application/json

{
  "charging_periods": [
    {
      "start_date_time": "2023-04-26T11:56:53.000Z",
      "dimensions": [
        {
          "type": "ENERGY",
          "volume": 1
        }
      ],
      "tariff_id": "DEEULTDEFAULT"
    }
  ],
  "last_updated": "2023-04-26T08:26:43.157Z",
  "kwh": 3,
  "total_cost": {
    "excl_vat": 1
  }
}


###

# JK EMP Roaming Connection
# 55093f77ea08489da051565acd921b11 << is the correct token
PUT http://localhost:3000/ocpi/2.2/sessions/DE/EUL/DEEUL792C7AF7F7F047E795C9C785D699XXX
Authorization: Token 55093f77ea08489da051565acd921b11
Content-Type: application/json

{
  "id": "DEEUL792C7AF7F7F047E795C9C785D699BBB",
  "country_code": "DE",
  "party_id": "EUL",
  "start_date_time": "2023-04-23T18:24:07.000Z",
  "kwh": 0,
  "cdr_token": { "uid": "123456789", "type": "RFID", "contract_id": "XXX" },
  "auth_method": "COMMAND",
  "location_id": "EULS000X1",
  "evse_uid": "08b0b412-546b-4b32-a265-2845b705efa7",
  "connector_id": "1",
  "currency": "EUR",
  "charging_periods": [
    {
      "start_date_time": "2023-04-23T18:32:27.000Z",
      "dimensions": [ { "type": "ENERGY", "volume": 0 } ],
      "tariff_id": "DEEULTDEFAULT"
    }
  ],
  "total_cost": { "excl_vat": 1 },
  "status": "ACTIVE",
  "last_updated": "2023-04-25T10:24:20.141Z",
  "authorization_reference": "12219832urjifr2i0qdßj"
}

###

PATCH http://localhost:3000/ocpi/2.2/sessions/DE/EUL/DEEULCA8817AB5C694F95B4359363384574F
Authorization: Token 55093f77ea08489da051565acd921b11
Content-Type: application/json

{
  "id": "DEEULCA8817AB5C694F95B4359363384574F",
  "country_code": "DE",
  "party_id": "EUL",
  "start_date_time": "2023-05-08T12:11:30.000Z",
  "kwh": 0,
  "cdr_token": { "uid": "FE6E90749B860A224D1D", "type": "RFID", "contract_id": "XXX" },
  "auth_method": "COMMAND",
  "authorization_reference": "88073fc3bbe4ada8c651",
  "location_id": "DEEULS000X",
  "evse_uid": "726a66d4-df2a-415b-86b0-67948e85befa",
  "connector_id": "1",
  "currency": "EUR",
  "charging_periods": [],
  "total_cost": { "excl_vat": 1 },
  "status": "ACTIVE",
  "last_updated": "2023-05-08T12:11:35.414Z"
}


###
GET https://beta.ocpi.longship.io/ocpi/2.2/sessions
Authorization: Token 7d5bfda3b8cd4a24874575b9ce198d44
Content-Type: application/json


###
DE/EUL/DEEULB1D5722D83C7494FA220ABBA4DF0D50


###
GET https://beta.ocpi.longship.io/ocpi/2.2/sessions?date_from=2023-04-26T09:36:58.428Z&date_to=2023-04-27T09:36:58.428Z&offset=0&limit=50
Authorization: Token 7d5bfda3b8cd4a24874575b9ce198d44
Content-Type: application/json


GET http://localhost:3000/ocpi/2.2/sessi

###

PUT  http://localhost:3000/ocpi/2.2/sessions/DE/EUL/DEEULE27FC4E6F8E5473DA604D540E5C70DB
Authorization: Token 55093f77ea08489da051565acd921b11
Content-Type: application/json

{
  "id": "DEEULE27FC4E6F8E5473DA604D540E5C70DB",
  "country_code": "DE",
  "party_id": "EUL",
  "start_date_time": "2023-05-08T12:27:04.000Z",
  "kwh": 0,
  "cdr_token": { "uid": "BFFA4AB29286A296D2C3", "type": "RFID", "contract_id": "XXX" },
  "auth_method": "COMMAND",
  "authorization_reference": "0e305d984126292d6755",
  "location_id": "DEEULS000X",
  "evse_uid": "726a66d4-df2a-415b-86b0-67948e85befa",
  "connector_id": "1",
  "currency": "EUR",
   "charging_periods": [],
   "total_cost": { "excl_vat": 1 },
   "status": "ACTIVE",
   "last_updated": "2023-05-08T12:31:07.082Z"
}