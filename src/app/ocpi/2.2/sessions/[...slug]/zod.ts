import { z } from "zod";

const CdrTokenSchema = z.object({
  uid: z.string(),
  type: z.string(),
  contract_id: z.string(),
});

const ChargingPeriodsSchema = z.object({
  start_date_time: z.string(),
  dimensions: z.array(
    z.object({
      type: z.string(),
      volume: z.number(),
    })
  ),
  tariff_id: z.string().optional().default(""),
});

const DataSchema = z.object({
  id: z.string(),
  country_code: z.string(),
  party_id: z.string(),
  start_date_time: z.string(),
  end_date_time: z.string().optional().default(""),
  kwh: z.number(),
  cdr_token: CdrTokenSchema,
  auth_method: z.string(),
  location_id: z.string(),
  evse_uid: z.string(),
  connector_id: z.string(),
  authorization_reference: z.string(),
  currency: z.string(),
  charging_periods: z.array(ChargingPeriodsSchema),
  total_cost: z.object({
    excl_vat: z.number(),
  }),
  status: z.string(),
  last_updated: z.string(),
});

export const ApiResponseSchema = z.object({
  data: z.array(DataSchema),
  status_code: z.number(),
  status_message: z.string(),
  timestamp: z.string(),
});
