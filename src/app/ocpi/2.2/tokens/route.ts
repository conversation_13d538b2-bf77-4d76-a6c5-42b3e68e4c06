import { type NextRequest, NextResponse } from "next/server";

export const revalidate = 0;
export function GET(_request: NextRequest) {
  const token = [
    {
      country_code: "DE",
      party_id: "EUL",
      uid: "123456789",
      type: "RFID",
      contract_id: "NL8ACC12E46L89",
      visual_number: "DF000-2001-8999-1",
      issuer: "Eulektro",
      group_id: "DF000-2001-8999",
      valid: true,
      whitelist: "ALWAYS",
      last_updated: "2023-03-29T22:39:09Z",
    },
  ];

  return NextResponse.json({
    data: token,
    status_code: 1000,
    status_message: "Success",
    timestamp: new Date().toISOString(),
  });
}
