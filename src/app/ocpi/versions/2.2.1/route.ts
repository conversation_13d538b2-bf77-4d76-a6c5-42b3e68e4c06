import { type NextRequest, NextResponse } from "next/server";
import { env } from "~/env.mjs";

export function GET(_request: NextRequest) {

  const OCPI_DOMAIN = env.OCPI_DOMAIN;

  const data = {
    version: "2.2.1",
    endpoints: [
      {
        identifier: "credentials",
        role: "BOTH",
        url: `${OCPI_DOMAIN}/ocpi/2.2/credentials`,
      },
      {
        identifier: "tokens",
        role: "BOTH",
        url: `${OCPI_DOMAIN}/ocpi/2.2/tokens`,
      },
      {
        identifier: "commands",
        role: "SENDER",
        url: `${OCPI_DOMAIN}/ocpi/2.2/commands`,
      },
      {
        identifier: "locations",
        role: "BOTH",
        url: `${OCPI_DOMAIN}/ocpi/2.2/locations`,
      },
      {
        identifier: "sessions",
        role: "BOTH",
        url: `${OCPI_DOMAIN}/ocpi/2.2/sessions`,
      },
      {
        identifier: "cdrs",
        role: "RECEIVER",
        url: `${OCPI_DOMAIN}/ocpi/2.2/cdrs`,
      },
      {
        identifier: "tariffs",
        role: "RECEIVER",
        url: `${OCPI_DOMAIN}/ocpi/2.2/tariffs`,
      },
    ],
  };

  return NextResponse.json({
    data: data,
    status_code: 1000,
    status_message: "Success",
    timestamp: new Date().toISOString(),
  });
}
