import { type NextRequest, NextResponse } from "next/server";
import {env} from "~/env.mjs";

export function GET(request: NextRequest) {

  const host = request.headers.get('host');
  if (!host) {
    return NextResponse.json({
      status_code: 3000,
      status_message: "Error",
      timestamp: new Date().toISOString(),
    });
  }

  const versions = [
    {
      version: "2.2.1",
      url: `${env.OCPI_DOMAIN}/ocpi/versions/2.2.1`, // <PERSON><PERSON><PERSON><PERSON> dies durch die URL deines eMSP-Server-Endpunkts für 2.2.1
    },
  ];

  return NextResponse.json({
    data: versions,
    status_code: 1000,
    status_message: "Success",
    timestamp: new Date().toISOString(),
  });
}
