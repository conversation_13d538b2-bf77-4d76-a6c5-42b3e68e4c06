"use client";

import { StatusEnum } from "../../../../../../prisma/client";
import Image from "next/image";
import { useState } from "react";
import Link from "~/utils/Link";
import useTranslation from "next-translate/useTranslation";
import Trans from "next-translate/Trans";
import { transformSlugToEvseId } from "~/utils/transform/transform";
import { getCpoOperatorId } from "~/styles/oucolors/whiteLabelColors";
import { useSearchParams } from "next/navigation";
import { FaMoneyBill } from "react-icons/fa";
import { RiBillLine } from "react-icons/ri";

interface Props {
  params: {
    evse: string;
    status: StatusEnum;
  };
}

const Page = ({ params }: Props) => {
  const status = params.status;
  const searchParams = useSearchParams();
  const paymentIntentId = searchParams.get("paymentIntentId");
  const { t } = useTranslation("common");
  const [memorizeEmail, setMemorizeEmail] = useState<boolean>(false);
  const [invoiceMail, setInvoiceMail] = useState("");
  const [openInvoiceBox, setOpenInvoiceBox] = useState(false);
  const operatorId = getCpoOperatorId(transformSlugToEvseId(params?.evse));
  let helpBoxHeader = () => {
    return <>{t("cable_locked")}</>;
  };
  let helpBoxText = () => {
    return <>{t("first_disconnect_from_car")}</>;
  };

  const sendInvoicePerMail = async () => {
    setOpenInvoiceBox(false);
    try {
      if (invoiceMail) {
        const response = await fetch(
          `${window.location.origin}/api/stripe/updateMail`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: invoiceMail,
              paymentIntentId: paymentIntentId,
            }),
          }
        );
        if (response.status !== 200) {
        }
      }
    } catch (error) {}
  };

  if (status == StatusEnum.STOP_SESSION_REJECTED) {
    helpBoxHeader = () => {
      return (
        <>
          <Trans
            i18nKey={"common:timeout_cancel"}
            components={[<br key={"key_timeout_cancel"} />]}
          />
        </>
      );
    };
    helpBoxText = () => {
      return (
        <>
          <Trans
            i18nKey={"common:disconnect_and_godbye"}
            components={[<br key={"key_disconnect_and_godbye"} />]}
          />
        </>
      );
    };
  }

  let initOpenHelpBox = false;
  if (status == StatusEnum.STOP_SESSION_REJECTED) {
    initOpenHelpBox = true;
  }

  const [openHelpBox, setOpenHelpBox] = useState(initOpenHelpBox);

  const RenderInvoiceBox = () => {
    return (
      openInvoiceBox && (
        <div
          className={`h-content absolute left-0
          top-[150px] flex w-full transform flex-col rounded-t-3xl bg-white pb-8 font-sansPro transition-all duration-500 ease-in-out`}
        >
          <div
            className={
              "flex h-full flex-col justify-between gap-5 px-6 text-2xl text-primary"
            }
          >
            <div
              className={
                "mt-10 flex  h-full max-h-[50vh] max-h-[70svh] flex-grow flex-col gap-5"
              }
            >
              <div
                className={
                  "flex-shrink-0 justify-items-start font-sansPro font-bold"
                }
              >
                {t("request_invoice")}
                <div
                  onClick={() => setOpenInvoiceBox(false)}
                  className={
                    "absolute right-5 top-10 cursor-pointer text-secondary"
                  }
                >
                  <Image
                    alt={"close"}
                    src={"/commonImages/close.svg"}
                    width={25}
                    height={25}
                  />
                </div>
              </div>
              <div
                className={"flex flex-col gap-3 overflow-y-scroll text-2xl "}
              >
                <Trans
                  i18nKey={"common:email_invoice_label"}
                  components={[<br key={"key_email"} />]}
                />
              </div>
              <div className={"flex-shrink-0"}>
                <input
                  type={"email"}
                  value={invoiceMail}
                  className={
                    "peer w-full rounded-xl border p-3 text-center text-2xl text-primary"
                  }
                  placeholder={t("email_placeholder")}
                  onChange={(e) => {
                    setInvoiceMail(e.target.value);
                    if (memorizeEmail) {
                      localStorage.setItem("invoiceMail", e.target.value);
                    }
                  }}
                />
                <div className="mt-8">
                  <label className="text-md ml-2 flex items-center  text-primary">
                    <input
                      type="checkbox"
                      checked={memorizeEmail}
                      className="mr-3 h-6 w-6"
                      onChange={(e) => {
                        if (!e.target.checked) {
                          localStorage.setItem("invoiceMail", "");
                          setMemorizeEmail(false);
                        } else {
                          setMemorizeEmail(true);
                          localStorage.setItem("invoiceMail", invoiceMail);
                        }
                      }}
                    />
                    <span>{t("memorize_mail")}</span>
                  </label>
                </div>
                <span className="invisible text-sm text-red-600 peer-invalid:visible">
                  {" "}
                  {t("pls_valid_email")}
                </span>
              </div>
            </div>
            <div className={"flex-shrink-0"}>
              <button
                onClick={() => void sendInvoicePerMail()}
                className={"btn bg-primary text-secondary"}
              >
                {t("ok")}
              </button>
            </div>
          </div>
        </div>
      )
    );
  };

  return (
    <div className={" px-5 text-primary "}>
      <div className={"mt-5 text-center text-2xl  font-bold"}>
        {t("session_finished")}
      </div>
      <div className={"mt-5"}>
        <Trans
          i18nKey={"common:thanks_for_charging_endscreen"}
          components={[
            <h1 className={"text-2xl font-bold"} key={"key_thx"} />,
            <span key="you_charged" className={"text-xl"}></span>,
          ]}
        />

        <div className={"mt-2 text-2xl font-bold"}>
          {t("cannot_remove_cable")}
          <button
            onClick={() => setOpenHelpBox(!openHelpBox)}
            className={"btn bg-primary text-secondary"}
          >
            {t("help")}
          </button>
        </div>

        <div className={"mt-2 text-2xl font-bold"}>
          {t("request_invoice")}?
          <button
            onClick={() => setOpenInvoiceBox(true)}
            className={"btn bg-primary text-secondary"}
          >
            {t("request_invoice")}
          </button>
        </div>

        <div className={"w-100 mt-4 flex flex-row items-center gap-2 text-2xl"}>
          <RiBillLine />
          <a href={`${window.location.origin}/invoice`}>Download Portal</a>
        </div>
        {operatorId && operatorId == "EUL" && (
          <>
            <div className={"mt-5 text-2xl font-bold"}>{t("more_info")}</div>

            <div className={"text-xl"}>
              <div>{t("interested_in_eulektro")}</div>
              <div className={"flex items-end text-xl"}>
                <span className={"flex-grow"}>{t("follow_us")}</span>
                <Link
                  href={"https://www.instagram.com/eulektro/"}
                  className={"inline pl-5 text-center text-primary"}
                >
                  <Image
                    className={"mr-5 inline-block"}
                    alt={"Instagram"}
                    src={"/instagram.svg"}
                    width={50}
                    height={50}
                  />
                </Link>
                <Link
                  href={"https://www.facebook.com/eulektro/"}
                  className={"inline-block text-center text-primary"}
                >
                  <Image
                    className={"inline-block"}
                    alt={"Instagram"}
                    src={"/facebook.svg"}
                    width={50}
                    height={50}
                  />
                </Link>
              </div>
            </div>
          </>
        )}
      </div>
      <div
        className={`absolute left-0 ${
          openHelpBox ? "top-[150px] " : " top-screen"
        }  h-content flex w-full transform flex-col rounded-t-3xl bg-white pb-8 transition-all duration-500 ease-in-out`}
      >
        <div
          className={
            "flex h-full flex-col justify-between gap-5 px-6 text-2xl text-primary"
          }
        >
          <div
            className={
              "mt-10 flex  h-full max-h-[50vh] max-h-[70svh] flex-grow flex-col gap-5 overflow-y-auto"
            }
          >
            <div className={"flex-shrink-0 font-sansPro font-bold"}>
              {helpBoxHeader()}
              <div
                onClick={() => setOpenHelpBox(false)}
                className={"absolute right-5 top-10"}
              >
                <Image
                  alt={"close"}
                  src={"/commonImages/close.svg"}
                  className={"cursor-pointer"}
                  width={25}
                  height={25}
                />
              </div>
            </div>
            <div className={"text-2xl"}>{helpBoxText()}</div>
          </div>
        </div>
        <div className={" px-5"}>
          <button
            onClick={() => setOpenHelpBox(false)}
            className={"btn bg-primary text-secondary"}
          >
            {t("ok")}
          </button>
        </div>
      </div>
      {RenderInvoiceBox()}
    </div>
  );
};

export default Page;
