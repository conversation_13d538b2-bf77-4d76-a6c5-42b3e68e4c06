import { transformSlugToEvseId } from "~/utils/transform/transform";
import {
  getCpoOperatorId,
  getWhitelabelRootByOperator,
  whiteLabelColors,
} from "~/styles/oucolors/whiteLabelColors";
import LanguageSelector from "~/utils/translation/chooseLanguage";
import Image from "next/image";
export default function Layout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { evse: string };
}) {
  const evseId = transformSlugToEvseId(params.evse);
  const theme = getWhitelabelRootByOperator(getCpoOperatorId(evseId) ?? "EUL");

  return (
    <div
      style={
        whiteLabelColors[getCpoOperatorId(evseId) ?? "EUL"] ??
        whiteLabelColors.Default
      }
      className={"relative mx-auto flex h-screen max-w-lg flex-col bg-main "}
    >
      <div id={"header"} className={"flex-basis-[200] relative h-[200px] "}>
        <Image
          width={720}
          height={422}
          alt={"hintergrund"}
          src={theme?.hero}
          className={
            "absolute h-[211px] h-full w-full bg-clip-content bg-top bg-no-repeat object-cover"
          }
          loading={"lazy"}
        />

        {theme?.logo && (
          <Image
            className={
              "absolute right-[15px] top-[10px]  w-[100px]  xs:top-[15px] xs:w-[120px]"
            }
            alt={"Firmenlogo"}
            src={theme.logo}
            width={146}
            height={58}
            loading={"lazy"}
          />
        )}

        {theme.head && (
          <>
            <div
              className={"absolute bottom-0 left-0 h-[25px] w-[50%] bg-primary"}
            ></div>
            <Image
              className={
                "absolute bottom-0 left-1/2 h-[100px] w-[100px] -translate-x-1/2 transform xs:h-[125px] xs:w-[125px]"
              }
              alt={"kopfzeilenbild"}
              src={theme.head}
              width={186}
              height={187}
              loading={"lazy"}
            />
          </>
        )}
      </div>
      <LanguageSelector key={"1"} theme={theme} />
      <div id={"content"} className={"flex-grow"}>
        {children}
      </div>
    </div>
  );
}
