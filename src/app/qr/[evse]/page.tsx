import Restore from "~/app/qr/[evse]/start/restore";
import { getPriceForStationByEvseId } from "~/utils/data/getBy";
import { SendDebugMessage } from "~/utils/chat/sendMessage";
import useTranslation from "next-translate/useTranslation";
import Trans from "next-translate/Trans";
import Link from "~/utils/Link";
import { transformSlugToEvseId } from "~/utils/transform/transform";

interface Props {
  params: {
    evse: string; // evse = chargepointID
  };
}

const Page = async ({ params }: Props) => {
  const evseId = transformSlugToEvseId(params.evse);
  const { price, current_type } = await getPriceForStationByEvseId(evseId);
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { t, lang } = useTranslation("common");
  if (!price) {
    void (await SendDebugMessage(
      `<PERSON><PERSON> Preis für diesen Standort und Steckertyp gefunden ${params.evse}`
    ));
    return (
      <div className={"flex flex-col gap-5 px-5 text-primary"}>
        <div className={"mt-5"}>{t("no_price_found")}</div>
        <div>{t("try_another_qr")}</div>
      </div>
    );
  }

  const renderStartScreenWithPrice = () => {
    return (
      <div className={"flex h-full flex-col text-secondary"}>
        <div className={""}>
          <div className={"font-sansPro"}>
            <div className={"mt-5 text-center text-base"}>
              <span
                className={"text-2xl font-bold leading-[1.5rem] text-primary"}
              >
                {t("welcome")}
              </span>
            </div>

            <div
              className={
                "  -bg-[center_top_0rem] bg-adhocStep5 bg-cover bg-no-repeat pb-10  "
              }
            >
              <div
                className={
                  "mx-5 mt-5 flex flex-col items-center justify-center gap-5 text-secondary"
                }
              >
                <div className={"mt-10 flex flex-col gap-0"}>
                  <div
                    className={
                      "flex flex-row justify-center text-center text-2xl font-bold"
                    }
                  >
                    {t("ad_hoc_tariff")} {current_type}
                  </div>
                  <div
                    className={
                      "text-center text-7xl font-bold leading-[4.5rem]"
                    }
                  >
                    {price.energy_price.toLocaleString(lang, {
                      style: "currency",
                      currency: "EUR",
                    })}
                    <span className={"text-2xl "}> / kWh*</span>
                  </div>
                  <div
                    className={
                      " w-full text-center text-2xl font-bold leading-[1.5rem]"
                    }
                  >
                    {price?.session_fee > 0 && (
                      <div>
                        {price.session_fee.toLocaleString(lang, {
                          style: "currency",
                          currency: "EUR",
                        })}{" "}
                        {t("start_fee")}*
                      </div>
                    )}
                    {price?.blocking_fee > 0 && (
                      <>
                        <div className={"mt-2 text-lg"}>
                          {t("beginning")}{" "}
                          {(price.blocking_fee_start / 60).toLocaleString(lang)}
                          {"h "}
                          {price.blocking_fee.toLocaleString(lang, {
                            style: "currency",
                            currency: "EUR",
                          })}
                          /{t("minute")}*
                        </div>
                        <div className={"text-lg"}>
                          {t("max")}
                          {". "}
                          {price.blocking_fee_max.toLocaleString(lang, {
                            style: "currency",
                            currency: "EUR",
                          })}{" "}
                          {t("blocking_fee")}*
                        </div>
                      </>
                    )}
                  </div>
                </div>
                <div
                  className={
                    "mt-5 flex flex-col items-start self-start  text-sm xs:text-base"
                  }
                >
                  <div>
                    <Trans
                      i18nKey="common:terms_of_condition"
                      components={[
                        <a
                          key={"bedingungen"}
                          className={"font-bold underline"}
                          href={
                            "/docs/Nutzungs-und-Ladebedingungen_Eulektro.pdf"
                          }
                        />,
                        <a
                          key={"bedingungen2"}
                          className={"font-bold underline"}
                          href={"https://eulektro.de/datenschutz"}
                        />,
                      ]}
                      values={{ count: 42 }}
                    />
                  </div>
                </div>
                <div className={" flex w-full flex-col gap-2  text-secondary"}>
                  <div className={"self-start text-sm  font-bold xs:text-base"}>
                    {t("prices_include_vat")}
                  </div>
                  <Link className={"w-full"} href={`/qr/${params.evse}/step2`}>
                    <button
                      className={
                        "w-full rounded-xl bg-primary p-3  text-2xl font-bold text-secondary"
                      }
                    >
                      {t("agree")}
                    </button>
                  </Link>
                </div>
              </div>
            </div>
            <Restore evse={params.evse} />
          </div>
        </div>
        <div className={"flex-grow bg-secondary"}></div>
      </div>
    );
  };

  return renderStartScreenWithPrice();
};

export default Page;
