"use client";

import { z } from "zod";
import { useEffect, useState } from "react";
import { PaymentIntentIdSchema } from "~/utils/schema/paymentIntentIds";
import { transformEvseToEvseSlugId } from "~/utils/transform/transform";
import Link from "~/utils/Link";
import useTranslation from "next-translate/useTranslation";

const RestoreDataSchema = z.object({
  data: z.object({
    createdAt: z.string(),
    id: z.string(),
    charger: z.string(),
  }),
  status_code: z.number(),
  status_message: z.string(),
  timestamp: z.string(),
});

interface ActivePaymentIntent {
  createdAt: string;
  id: string;
  charger: string;
}

// ToDO: evse könnte weg glaub
interface Props {
  evse: string;
}

const RestoreView = ({ evse }: Props) => {
  const [paymentIntentIdFromLocalStorage, setPaymentIntentIdFromLocalStorage] =
    useState<string>("");
  const { t } = useTranslation("common");

  const [hideActiveSessions, setHideActiveSessions] = useState<boolean>(false);

  const [activePaymentIntentId, setActivePaymentIntentId] = useState<
    ActivePaymentIntent | undefined
  >(undefined);

  useEffect(() => {
    if (typeof window !== undefined) {
      const validateDataFromStorage = PaymentIntentIdSchema.safeParse(
        localStorage.getItem("paymentIntentId")
      );
      if (validateDataFromStorage.success) {
        setPaymentIntentIdFromLocalStorage(validateDataFromStorage.data);
      }
    }
  }, []);

  useEffect(() => {
    if (paymentIntentIdFromLocalStorage !== "") {
      const fetchData = async () => {
        const response = await fetch(
          `${window.location.origin}/api/adhoc/status/restore`,
          {
            method: "POST",
            body: JSON.stringify({
              paymentIntentId: paymentIntentIdFromLocalStorage,
            }),
          }
        );

        const jsonData = RestoreDataSchema.safeParse(await response.json());

        if (jsonData.success) {
          setActivePaymentIntentId(jsonData.data.data);
        }
      };

      fetchData().catch(() => {
        return;
      });
    }
  }, [paymentIntentIdFromLocalStorage]);

  if (activePaymentIntentId && !hideActiveSessions) {
    return (
      <div
        className={
          "absolute left-0 top-0 h-screen w-full bg-main px-5 text-primary"
        }
      >
        <div className={" mt-5 text-2xl font-bold"}>{t("welcome")}</div>
        <div className={"mb-4 text-2xl"}>{t("active_session_found")}</div>

        <div
          className={"flex flex-grow items-center gap-1"}
          key={activePaymentIntentId.id}
        >
          <div className={"text-1xl mb-0 basis-1/2  font-bold"}>
            {new Date(activePaymentIntentId.createdAt).toLocaleString()}
            <br />
            {activePaymentIntentId.charger}
          </div>
          <Link
            className={"basis-1/2"}
            href={`/qr/${transformEvseToEvseSlugId(
              activePaymentIntentId.charger
            )}/status/${activePaymentIntentId.id}`}
          >
            <button
              className={
                "w-full rounded-xl bg-primary p-3  font-bold text-secondary"
              }
            >
              {t("view_session")}
            </button>
          </Link>
        </div>

        <button
          onClick={() => setHideActiveSessions(true)}
          className={
            "mt-10 w-full rounded-xl bg-primary p-3  font-bold text-secondary"
          }
        >
          {t("start_another_session")}
        </button>
      </div>
    );
  }
  return <></>;
};

export default RestoreView;
