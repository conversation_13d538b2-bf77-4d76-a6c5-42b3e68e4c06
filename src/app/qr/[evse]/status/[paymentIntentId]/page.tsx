"use client";
import { use<PERSON>allback, useEffect, useMemo, useState } from "react";
import { StatusEnum } from "../../../../../../prisma/client";
import Image from "next/image";
import { AdHocStatusChargingSchema } from "~/app/qr/[evse]/step4/renderState";
import { transformSlugToEvseId } from "~/utils/transform/transform";
import { convertToSeconds, convertToTimeStr } from "~/utils/date/date";
import { useRouter } from "next/navigation";
import { useInterval, useWindowSize } from "@react-hooks-library/core";
import BoldifyLastTwoChars from "~/utils/format/BoldifyLastTwoChars";
import Spinner from "~/utils/spinner/Spinner";
import Trans from "next-translate/Trans";
import useTranslation from "next-translate/useTranslation";
import { z } from "zod";

const INTERPOLATION_TIME_LIMIT_IN_SECONDS = 60 * 6;

interface Props {
  params: {
    evse: string;
    paymentIntentId: string;
  };
}
const EmailSchema = z.string().email();
enum PageState {
  INIT,
  STOP_SESSION_REQUEST,
  CHARGING,
  SESSION_NOT_FOUND,
  STOP_SESSION_SUCCESS,
}

const Page = ({ params }: Props) => {
  const [pageState, setPageState] = useState<number>(PageState.INIT);
  const [fetchInterval, setFetchInterval] = useState(1000); // initially set to 1 second
  const [isSlowIntervall, setSlowIntervall] = useState(false);
  const [openInvoiceBox, setOpenInvoiceBox] = useState(false);
  const [invoiceMail, setInvoiceMail] = useState("");
  const { t, lang } = useTranslation("common");
  const [memorizeEmail, setMemorizeEmail] = useState<boolean>(false);

  const [chargingData, setChargingData] = useState({
    status: "",
    kwh: 0.0,
    duration: "00:00:00",
  });
  const [duration, setDuration] = useState(chargingData.duration);
  const langGetSuffix = useMemo(() => {
    if (lang.toLowerCase() != "de") {
      return "&lang=" + lang;
    }
    return "";
  }, [lang]);
  // add this to your component state
  const [kwhData, setKwhData] = useState({
    previous: { kwh: 0.0, timestamp: 0 },
    current: { kwh: 0.0, timestamp: 0 },
    powerInKw: 0.0,
  });

  const router = useRouter();

  const { height } = useWindowSize();

  const stopSession = async () => {
    setPageState(PageState.STOP_SESSION_REQUEST);
    void (await fetch(
      `${window.location.origin}/api/adhoc/command/stopSession`,
      {
        method: "POST",
        body: JSON.stringify({
          paymentIntentId: params.paymentIntentId,
          evseId: transformSlugToEvseId(params.evse),
        }),
      }
    ));
    setSlowIntervall(false);
  };

  useEffect(() => {
    if (typeof window !== undefined) {
      const validateDataFromStorage = EmailSchema.safeParse(
        localStorage.getItem("invoiceMail")
      );
      if (validateDataFromStorage.success) {
        setInvoiceMail(validateDataFromStorage.data);
        setMemorizeEmail(true);
      }
    }
  }, []);

  useEffect(() => {
    // Funktion, die die Aktualisierung an den Server sendet
    const sendUpdateToServer = async () => {
      try {
        const response = await fetch(
          `${window.location.origin}/api/stripe/updateMail`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: invoiceMail,
              paymentIntentId: params.paymentIntentId,
            }),
          }
        );
        if (response.status !== 200) {
        }
      } catch (error) {}
    };

    if (invoiceMail) {
      // Timer-Variable, um den nächsten Fetch-Aufruf zu planen
      let updateTimeout: number;

      // Funktion zum Planen der Aktualisierung nach 5 Sekunden
      const scheduleUpdate = () => {
        clearTimeout(updateTimeout);
        updateTimeout = window.setTimeout(() => {
          void sendUpdateToServer();
        }, 5000);
      };

      // Planen Sie die Aktualisierung, wenn sich die invoiceMail ändert
      scheduleUpdate();

      // Bereinigungsfunktion, um den Timer bei Änderungen oder beim Aushängen zu löschen
      return () => {
        localStorage.setItem("invoiceMail", invoiceMail);
        clearTimeout(updateTimeout);
      };
    }
  }, [invoiceMail]);

  const fetchData = useCallback(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(
          `${window.location.origin}/api/adhoc/status/charging`,
          {
            method: "POST",
            body: JSON.stringify({ paymentIntentId: params.paymentIntentId }),
          }
        );
        const payload = AdHocStatusChargingSchema.safeParse(
          await response.json()
        );
        if (payload.success) {
          switch (payload.data.data.status) {
            case StatusEnum.CDR_RECEIVED:
            case StatusEnum.CAPTURED:
            case StatusEnum.CAPTURE_FAILED:
            case StatusEnum.REFUNDED:
            case StatusEnum.INVOICE_SENT:
            case StatusEnum.STOP_SESSION_REJECTED:
              router.push(
                `${window.location.origin}/qr/${params.evse}/end/${payload.data.data.status}?paymentIntentId=${params.paymentIntentId}${langGetSuffix}`
              );
              router.refresh();
              break;
            case StatusEnum.SESSION_RECEIVED:
              if (PageState.STOP_SESSION_REQUEST != pageState) {
                setPageState(PageState.CHARGING);
              }
              setDuration(payload.data.data.duration);

              // convert timestamp to milliseconds
              const timestamp = new Date(payload.data.timestamp).getTime();

              if (
                payload.data.data.kwh !== kwhData.current.kwh &&
                payload.data.data.kwh > 0
              ) {
                const kWhDiff = payload.data.data.kwh - kwhData.current.kwh;
                const timeDiff = timestamp - kwhData.current.timestamp;
                let powerInKw = 0;
                if (
                  kwhData.current.kwh > 0 &&
                  kwhData.current.timestamp > 0 &&
                  timeDiff > 1000 * 30 // 30 seconds minimum
                ) {
                  // Check if we have previous data
                  powerInKw = kWhDiff / (timeDiff / 1000 / 60 / 60);
                }

                // if the new kWh from the server has changed
                setKwhData({
                  previous: { ...kwhData.current },
                  current: { kwh: payload.data.data.kwh, timestamp: timestamp },
                  powerInKw: powerInKw,
                });

                setChargingData(payload.data.data);
              }

              break;
            case StatusEnum.START_ACCEPTED:
              setPageState(PageState.INIT);
              break;
            default:
              setPageState(PageState.SESSION_NOT_FOUND);
          }
        }
      } catch (error) {
        console.log(error);
      }
    };
    void fetchData();
  }, [pageState, kwhData, langGetSuffix]);

  useInterval(fetchData, fetchInterval, {
    immediate: true,
  });

  useEffect(() => {
    if (isSlowIntervall) {
      const timer = setTimeout(() => {
        setFetchInterval(10000); // after 15 seconds, set the fetch interval to 10 seconds
      }, 15000);

      // this will clean up the timer when the component is unmounted or if the dependency changes
      return () => clearTimeout(timer);
    } else {
      setFetchInterval(1000); // after 10 seconds, set the fetch interval to 1 second
      const timer = setTimeout(() => {
        setSlowIntervall(true);
      }, 10000);

      // this will clean up the timer when the component is unmounted or if the dependency changes
      return () => clearTimeout(timer);
    }
  }, [isSlowIntervall]);

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;
    // only start the interpolation if the previous and current kWh are different
    if (kwhData.previous.kwh !== kwhData.current.kwh) {
      if (kwhData.previous.kwh === 0) {
        setChargingData({
          ...chargingData,
          kwh: kwhData.current.kwh,
        });
      } else {
        // start interpolation
        intervalId = setInterval(() => {
          if (pageState === PageState.CHARGING) {
            const elapsedTimeInSeconds =
              (Date.now() - kwhData.current.timestamp) / 1000;
            const elapsedTimeInHours = elapsedTimeInSeconds / 3600;
            if (elapsedTimeInSeconds > INTERPOLATION_TIME_LIMIT_IN_SECONDS) {
              // it seems that no kWh were charged since limit
              // so set the last known kWh and clear intervall
              setChargingData({
                ...chargingData,
                kwh: kwhData.current.kwh,
              });

              // stop interpolating
              if (intervalId) {
                clearInterval(intervalId);
              }
            }
            const interpolatedKwh =
              kwhData.current.kwh + kwhData.powerInKw * elapsedTimeInHours;

            setChargingData({
              ...chargingData,
              kwh: interpolatedKwh,
            });
          }
        }, 1000); // adjust interval as needed
      }
    }
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [kwhData, pageState]);

  useEffect(() => {
    const durationIntervalId = window.setInterval(() => {
      if (pageState === PageState.CHARGING) {
        const currentTimeInSeconds = convertToSeconds(duration);
        const updatedTimeInSeconds = currentTimeInSeconds + 1;
        setDuration(convertToTimeStr(updatedTimeInSeconds));
      }
    }, 1000);

    return () => {
      if (durationIntervalId) {
        clearInterval(durationIntervalId);
      }
    };
  }, [duration, pageState]);

  if (!params.evse) {
    return <div>EVSE ID ungültig</div>;
  }

  const RenderInvoiceBox = () => {
    return (
      openInvoiceBox && (
        <div
          className={`h-content absolute left-0
          top-[150px] flex w-full transform flex-col rounded-t-3xl bg-white pb-8 font-sansPro transition-all duration-500 ease-in-out`}
        >
          <div
            className={
              "flex h-full flex-col justify-between gap-5 px-6 text-2xl text-primary"
            }
          >
            <div
              className={
                "mt-10 flex  h-full max-h-[50vh] max-h-[70svh] flex-grow flex-col gap-5"
              }
            >
              <div
                className={
                  "flex-shrink-0 justify-items-start font-sansPro font-bold"
                }
              >
                {t("request_invoice")}
                <div
                  onClick={() => setOpenInvoiceBox(false)}
                  className={
                    "absolute right-5 top-10 cursor-pointer text-secondary"
                  }
                >
                  <Image
                    alt={"close"}
                    src={"/commonImages/close.svg"}
                    width={25}
                    height={25}
                  />
                </div>
              </div>
              <div
                className={"flex flex-col gap-3 overflow-y-scroll text-2xl "}
              >
                <Trans
                  i18nKey={"common:email_invoice_label"}
                  components={[<br key={"key_email"} />]}
                />
              </div>
              <div className={"flex-shrink-0"}>
                <input
                  type={"email"}
                  value={invoiceMail}
                  className={
                    "peer w-full rounded-xl border p-3 text-center text-2xl text-primary"
                  }
                  placeholder={t("email_placeholder")}
                  onChange={(e) => {
                    setInvoiceMail(e.target.value);
                    if (memorizeEmail) {
                      localStorage.setItem("invoiceMail", e.target.value);
                    }
                  }}
                />
                <div className="mt-8">
                  <label className="text-md ml-2 flex items-center  text-primary">
                    <input
                      type="checkbox"
                      checked={memorizeEmail}
                      className="mr-3 h-6 w-6"
                      onChange={(e) => {
                        if (!e.target.checked) {
                          localStorage.setItem("invoiceMail", "");
                          setMemorizeEmail(false);
                        } else {
                          setMemorizeEmail(true);
                          localStorage.setItem("invoiceMail", invoiceMail);
                        }
                      }}
                    />
                    <span>{t("memorize_mail")}</span>
                  </label>
                </div>
                <span className="invisible text-sm text-red-600 peer-invalid:visible">
                  {" "}
                  {t("pls_valid_email")}
                </span>
              </div>
            </div>
            <div className={"flex-shrink-0"}>
              <button
                onClick={() => setOpenInvoiceBox(false)}
                className={"btn bg-primary text-secondary"}
              >
                {t("save")}
              </button>
            </div>
          </div>
        </div>
      )
    );
  };

  switch (pageState) {
    case PageState.INIT:
      return (
        <div
          className={"h-content flex flex-col justify-between text-primary "}
        >
          <div className={"mt-5 text-center text-2xl font-bold"}>
            {t("connection_attempt")}
          </div>
          <div className={"text-center text-2xl text-black"}>
            <BoldifyLastTwoChars str={transformSlugToEvseId(params.evse)} />
          </div>

          <div
            className={
              "-bg-[center_top_0rem] items-end bg-adhocStep5 bg-cover bg-no-repeat "
            }
          >
            <div
              className={
                "mt-5  flex flex-col items-center justify-center gap-5 text-primary"
              }
            >
              <div className={"flex flex-col "}>
                <div
                  className={
                    "flex min-h-[60px] items-center text-center  text-7xl font-bold leading-[4.5rem]"
                  }
                >
                  <div className={"loader mx-auto text-center"}></div>
                </div>
                <div className={"text-center text-2xl"}>{t("charged")} kWh</div>
              </div>
              <div className={"flex flex-col "}>
                <div
                  className={
                    "flex min-h-[60px] items-center text-center text-3xl font-bold"
                  }
                >
                  <div className={"loader mx-auto text-center"}></div>
                </div>
                <div className={"text-center text-2xl"}>
                  {t("charging_time")}
                </div>
              </div>
              <div className={"mx-auto w-full px-5"}>
                <div
                  onClick={() => setOpenInvoiceBox(true)}
                  className={
                    "w-full cursor-pointer rounded-xl bg-primary p-3 text-center text-2xl  font-bold text-secondary"
                  }
                >
                  {t("request_invoice")}
                </div>
              </div>
              <div className={"mx-auto w-full px-5"}>
                <div
                  onClick={() => void stopSession()}
                  className={
                    "w-full cursor-pointer rounded-xl bg-primary p-3 text-center text-2xl  font-bold text-secondary"
                  }
                >
                  {t("stop_charging")}
                </div>
                {invoiceMail && (
                  <div className={"mt-5 text-sm"}>
                    {t("info_invoice", { invoiceMail: invoiceMail })}
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className={"flex-grow bg-secondary"}></div>
          {RenderInvoiceBox()}
        </div>
      );
    case PageState.STOP_SESSION_REQUEST:
      return (
        <div
          className={"h-content flex flex-col justify-between text-primary "}
        >
          <div className={"mt-5 text-center text-2xl font-bold"}>
            {t("session_will_be_stopped")}
          </div>
          <div className={"text-center text-2xl text-black"}>
            <BoldifyLastTwoChars str={transformSlugToEvseId(params.evse)} />
          </div>

          <div
            className={
              "-bg-[center_top_0rem] items-end bg-adhocStep5 bg-cover bg-no-repeat "
            }
          >
            <div
              className={
                "mt-[10%] flex flex-col items-center justify-center gap-5  text-secondary"
              }
            >
              <div className={"flex flex-col "}>
                <div
                  className={"text-center  text-7xl font-bold leading-[4.5rem]"}
                >
                  {chargingData.kwh.toLocaleString("de-DE", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}
                </div>
                <div className={"text-center text-2xl"}>{t("charged")} kWh</div>
              </div>
              <div className={"flex flex-col "}>
                <div className={"text-center text-2xl font-bold"}>
                  {duration}
                </div>
                <div className={"text-center text-2xl"}>
                  {t("charging_time")}
                </div>
              </div>
              <div className={"mx-auto w-full px-5"}></div>
              <div className={"mx-auto w-full px-5"}>
                <div
                  className={
                    " cursor-not-allowed rounded-xl bg-primary p-3 text-center text-2xl text-secondary"
                  }
                >
                  <Spinner inline={true} className={"mr-3"} />
                  {t("finishing_session")}
                </div>
                {invoiceMail && (
                  <div className={"mt-5 text-sm"}>
                    {t("info_invoice", { invoiceMail: invoiceMail })}
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className={"flex-grow bg-secondary"}></div>
        </div>
      );
    case PageState.CHARGING:
      return (
        <div
          className={"h-content flex flex-col justify-between text-primary "}
        >
          <div className={"mt-5 text-center text-2xl font-bold"}>
            {t("session_is_running")}
          </div>
          <div className={"text-center text-2xl text-black"}>
            <BoldifyLastTwoChars str={transformSlugToEvseId(params.evse)} />
          </div>

          <div
            className={
              "-bg-[center_top_0rem] items-end bg-adhocStep5 bg-cover bg-no-repeat "
            }
          >
            <div
              className={
                "mt-4 flex flex-col items-center justify-center gap-4 text-secondary"
              }
            >
              <div className={"flex flex-col "}>
                <div
                  className={`mb-0 text-center ${
                    height > 600 ? "text-6xl" : "text-2xl"
                  } font-bold`}
                >
                  {chargingData.kwh.toLocaleString("de-DE", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}
                </div>
                <div className={"text-center text-2xl"}>{t("charged")} kWh</div>
              </div>

              <div className={"flex flex-col "}>
                <div className={"text-center text-2xl font-bold"}>
                  {duration}
                </div>
                <div className={"text-center text-2xl"}>
                  {t("charging_time")}
                </div>
              </div>
              <div className={"mx-auto w-full px-5"}>
                <div
                  onClick={() => setOpenInvoiceBox(true)}
                  className={
                    "w-full cursor-pointer rounded-xl bg-primary p-3 text-center text-2xl  font-bold text-secondary"
                  }
                >
                  {t("request_invoice")}
                </div>
              </div>
              <div className={"mx-auto w-full px-5"}>
                <div
                  onClick={() => void stopSession()}
                  className={
                    "w-full cursor-pointer rounded-xl bg-primary p-3 text-center text-2xl  font-bold text-secondary"
                  }
                >
                  {t("stop_charging")}
                </div>
                {invoiceMail && (
                  <div className={"mt-5 text-sm"}>
                    {t("info_invoice", { invoiceMail: invoiceMail })}
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className={"flex-grow bg-secondary"}></div>
          {RenderInvoiceBox()}
        </div>
      );
  }
};

export default Page;
