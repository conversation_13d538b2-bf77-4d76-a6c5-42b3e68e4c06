import { getEmpByEvseId } from "~/utils/data/getBy";
import { transformSlugToEvseId } from "~/utils/transform/transform";
import useTranslation from "next-translate/useTranslation";
import Trans from "next-translate/Trans";
import Link from "../../../../utils/Link";

interface Props {
  params: {
    evse: string;
  };
}

const Page = async ({ params }: Props) => {
  const evseId = transformSlugToEvseId(params.evse);
  const emp = await getEmpByEvseId(evseId);
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { t } = useTranslation("common");
  if (!emp) {
    return (
      <div className={"flex flex-col gap-5"}>
        <div className={"mt-5"}>{t("adhoc_not_supported")}</div>
        <div>{t("please_try_later")}</div>
      </div>
    );
  }

  const amountToBLock = (emp.amount_to_block / 100).toLocaleString("de-DE", {
    style: "currency",
    currency: "EUR",
  });

  return (
    <div className={"px-5 font-sans text-primary "}>
      <div className={"mt-5 text-center  text-2xl"}>
        <span className={"font-bold"}>{t("payment_notice")}</span>
      </div>

      <div className={""}>
        <div className={"mt-5 font-sans text-xl"}>
          <div className={""}>
            <Trans
              i18nKey="common:reserve_security_amount"
              components={[
                <span key={"r1"} className={"font-bold"} />,
                <span key={"r2"} className={"font-bold"} />,
              ]}
              values={{ amountToBlock: amountToBLock }}
            />
          </div>
          <div className={""}>
            <Trans
              i18nKey="common:charge_cost"
              components={[
                <span key={"n1"} className={"font-bold"} />,
                <span key={"n2"} className={"font-bold"} />,
                <span key={"n3"} className={"font-bold"} />,
              ]}
            />
          </div>
          <div className={"mt-3 font-bold"}>
            {t("plug_in_charging_cable")} 🔌
          </div>
          <div className={"text-sm"}>{t("follow_signs")}</div>
        </div>
        <Link href={`/qr/${params.evse}/step3`}>
          <button
            className={
              "mt-5 w-full  rounded-xl bg-primary p-3 text-2xl  font-bold text-secondary"
            }
          >
            {t("proceed_to_payment")}
          </button>
        </Link>
      </div>
    </div>
  );
};

export default Page;
