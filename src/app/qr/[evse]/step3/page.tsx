import Payment from "~/app/qr/[evse]/step3/stripe/Payment";
import useTranslation from "next-translate/useTranslation";

interface Props {
  params: {
    evse: string;
  };
}

const Page = ({ params }: Props) => {
  const evse = params.evse;
  const { t } = useTranslation("common");

  return (
    <div className={"bg-main px-5 text-primary "}>
      <div className={"mt-5 text-center text-2xl "}>
        <span className={"font-bold"}>{t("payment_data")}</span>
      </div>
      <div className={""}>
        <Payment evse={evse} />
      </div>
    </div>
  );
};

export default Page;
