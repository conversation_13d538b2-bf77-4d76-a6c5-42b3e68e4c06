"use client";

import {
  PaymentElement,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import { type FormEvent, useMemo, useState } from "react";
import useTranslation from "next-translate/useTranslation";
import Spinner from "~/utils/spinner/Spinner";

type Props = {
  paymentIntentId: string;
  evse: string;
  amountToBlock: number;
};

const CheckoutForm = ({
  paymentIntentId: _paymentIntentId,
  evse,
  amountToBlock,
}: Props) => {
  const stripe = useStripe();
  const elements = useElements();

  const [message, setMessage] = useState<string | null | undefined>(null);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const { t, lang } = useTranslation("common");
  const langGetSuffix = useMemo(() => {
    if (lang.toLowerCase() != "de") {
      return "?lang=" + lang;
    }
    return "";
  }, [lang]);
  const handleSubmit = async (e: FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js has not yet loaded.
      // Make sure to disable form submission until Stripe.js has loaded.
      return;
    }

    setIsProcessing(true);

    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        // Make sure to change this to your payment completion page
        return_url: `${window.location.origin}/qr/${evse}/step4${langGetSuffix}`,
      },
    });
    if (error.type === "card_error" || error.type === "validation_error") {
      setMessage(error.message);
    } else {
      setMessage(t("unknown_error"));
    }

    setIsProcessing(false);
    return;
  };

  const amountToBlockString = (amountToBlock / 100).toLocaleString("de-DE", {
    style: "currency",
    currency: "EUR",
  });

  return (
    <form
      id="payment-form"
      onSubmit={(event: FormEvent<HTMLFormElement>) => {
        event.preventDefault();
        void handleSubmit(event);
      }}
    >
      <>
        <PaymentElement id="payment-element" />
        <button
          className={
            "mt-5  w-full rounded-xl bg-primary p-3 text-2xl  font-bold text-secondary"
          }
          disabled={isProcessing || !stripe || !elements}
          id="submit"
        >
          <span id="button-text" className={"text-secondary"}>
            {isProcessing ? (
              <>
                {" "}
                <Spinner
                  inline={true}
                  className={"mr-3"}
                  text={t("auth_payment")}
                />{" "}
              </>
            ) : (
              `${amountToBlockString} ${t("accept")}*`
            )}
          </span>
        </button>
        <div className={"mt-3 text-xs"}>{t("amount_will_be_blocked")}</div>
        {message && <div id="payment-message">{message}</div>}
      </>
    </form>
  );
};

export default CheckoutForm;
