"use client";
import { useEffect, useState } from "react";

import { Elements } from "@stripe/react-stripe-js";

import CheckoutForm from "~/app/qr/[evse]/step3/stripe/CheckoutForm";
import { z } from "zod";
import getStripe from "~/utils/stripe/get-stripejs";
import useTranslation from "next-translate/useTranslation";
import { type StripeElementLocale } from "@stripe/stripe-js";

import {
  getCpoOperatorId,
  whiteLabelColors,
} from "~/styles/oucolors/whiteLabelColors";
import { transformSlugToEvseId } from "~/utils/transform/transform";

interface Props {
  evse: string;
}

const CreateCheckoutSchema = z.object({
  success: z.boolean(),
  clientSecret: z.string(),
  paymentIntentId: z.string(),
  amountToBlock: z.number(),
});

const stripePromise = getStripe();

const Payment = ({ evse }: Props) => {
  const [clientSecret, setClientSecret] = useState<string>("");
  const [paymentIntentId, setPaymentIntentId] = useState<string | null>(null);
  const [amountToBlock, setAmountToBlock] = useState<number>(0);
  const { lang } = useTranslation();
  const evseID = transformSlugToEvseId(evse);
  const createPaymentIntent = async () => {
    const result = await fetch("/api/stripe/create-checkout-session", {
      method: "POST",
      body: JSON.stringify({}),
    });
    if (result.status == 200) {
      const zodCheckedPayload = CreateCheckoutSchema.safeParse(
        await result.json()
      );
      if (zodCheckedPayload.success) {
        setAmountToBlock(zodCheckedPayload.data.amountToBlock);
        setClientSecret(zodCheckedPayload.data.clientSecret);
        setPaymentIntentId(zodCheckedPayload.data.paymentIntentId);

        // overwrite paymentIntentId in localStorage
        localStorage.setItem(
          "paymentIntentId",
          zodCheckedPayload.data.paymentIntentId
        );
      }
    }
  };

  useEffect(() => {
    void (async () => {
      await createPaymentIntent();
    })();
  }, []);

  if (!evse) {
    return <div>Missing EVSE</div>;
  }

  const opId = getCpoOperatorId(evseID);
  let stripeColor = whiteLabelColors.Default?.stripe;
  if (opId) {
    stripeColor = whiteLabelColors[opId]?.stripe ?? stripeColor;
  }

  return (
    <div className={"mt-5"}>
      {clientSecret && paymentIntentId && (
        <Elements
          stripe={stripePromise}
          options={{
            locale: lang as StripeElementLocale, //ToDO elegantere lösung?
            clientSecret: clientSecret,
            appearance: {
              theme: "flat",
              variables: {
                colorText: stripeColor,
                fontFamily: "Source Sans Pro",
              },
            },
          }}
        >
          <CheckoutForm
            amountToBlock={amountToBlock}
            paymentIntentId={paymentIntentId}
            evse={evse}
          />
        </Elements>
      )}
    </div>
  );
};

export default Payment;
