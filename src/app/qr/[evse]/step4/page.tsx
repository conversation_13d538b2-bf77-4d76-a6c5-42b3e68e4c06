import RenderState from "~/app/qr/[evse]/step4/renderState";
import { prisma } from "~/server/db";
import { transformSlugToEvseId } from "~/utils/transform/transform";
import { env } from "~/env.mjs";
import { CpoHeaders } from "~/utils/cpo/header";
import { LocationResponseSchema } from "~/utils/schema/location";
import useTranslation from "next-translate/useTranslation";

export const revalidate = 0;

interface Props {
  params: {
    evse: string;
  };
}

const findLocationByEvse = async (evseUid: string) => {
  const location = await prisma.location.findFirst({
    where: {
      evses: {
        some: {
          evse_id: evseUid,
        },
      },
    },
  });

  if (!location) {
    return null;
  }
  const cpo_response = await fetch(
    `${env.LONGSHIP_DOMAIN}/ocpi/2.2/locations/${location.id}`,
    {
      method: "GET",
      headers: CpoHeaders,
    }
  );

  const cpo_json: unknown = await cpo_response.json();

  const location_from_cpo =
    LocationResponseSchema.nonstrict().safeParse(cpo_json);
  if (location_from_cpo.success) {
    return location_from_cpo.data.data;
  }

  return null;
};

const Page = async ({ params }: Props) => {
  const chargePointId = params.evse; // example: EUL_9999_01
  const evse = transformSlugToEvseId(chargePointId);
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { t } = useTranslation("common");

  if (!evse) {
    return <div>EVSE ID ungültig</div>;
  }
  const location = await findLocationByEvse(evse);
  if (!location) {
    return (
      <div className={"relative h-full px-5 text-primary"}>
        <div className={"mt-5 text-2xl text-primary"}>{t("qr_not_found")}</div>
        <div className={"text-1xl mt-2 text-primary"}>
          {t("chargepoint_not_active")}
        </div>
      </div>
    );
  }

  return (
    <div className={"relative h-full px-5 text-primary"}>
      <RenderState
        chargerLocation={location}
        evse={evse}
        chargePointId={chargePointId}
      />
    </div>
  );
};

export default Page;
