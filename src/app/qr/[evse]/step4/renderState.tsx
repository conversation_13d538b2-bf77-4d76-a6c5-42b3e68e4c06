"use client";

import { type ChangeEvent, useEffect, useMemo, useState } from "react";

import { z } from "zod";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import { convertToSeconds, convertToTimeStr } from "~/utils/date/date";
import { type LocationSchema } from "~/utils/schema/location";
import BoldifyLastTwoChars from "~/utils/format/BoldifyLastTwoChars";
import { type ResponseBody, ResponseCode } from "~/utils/response/response";
import { transformEvseToEvseSlugId } from "~/utils/transform/transform";
import Link from "~/utils/Link";
import useTranslation from "next-translate/useTranslation";
import Trans from "next-translate/Trans";
import { StatusEnum } from "../../../../../prisma/client";

interface Props {
  chargerLocation: z.infer<typeof LocationSchema>;
  evse: string;
  chargePointId: string;
}

export const ChargingDataSchema = z.object({
  kwh: z.number(),
  duration: z.string(),
  status: z.string(),
});

export const AdHocStatusChargingSchema = z.object({
  status_code: z.number(),
  status_message: z.string(),
  timestamp: z.string(),
  data: ChargingDataSchema,
});

export const revalidate = 0;

const RenderState = ({ chargerLocation, evse, chargePointId }: Props) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [openChargerChooseBox, setOpenChargerChooseBox] = useState(false);
  const [openCancelModal, setOpenCancelModal] = useState(false);

  const [selectedEvse, setSelectedEvse] = useState<string>(evse);

  const [state, setState] = useState(1);
  const [chargingData, setChargingData] = useState({
    status: "unknown",
    kwh: 0.0,
    duration: "00:00:00",
  });
  const [duration, setDuration] = useState(chargingData.duration);

  const [waitForResponseCounter, setWaitForResponseCounter] = useState(0);
  const { t, lang } = useTranslation("common");
  const startCharging = async () => {
    setWaitForResponseCounter(0);
    setState(2);

    void (await fetch(
      `${window.location.origin}/api/adhoc/command/startSession`,
      {
        method: "POST",
        body: JSON.stringify({
          paymentIntentId: searchParams.get("payment_intent"),
          evseId: selectedEvse,
        }),
      }
    ));
  };
  const langGetSuffix = useMemo(() => {
    if (lang.toLowerCase() != "de") {
      return "?lang=" + lang;
    }
    return "";
  }, [lang]);

  const findStatusByEvse = (
    location: z.infer<typeof LocationSchema>,
    evse_to_search: string
  ): string => {
    if (location) {
      return (
        location.evses.find((evse) => {
          if (evse.evse_id) {
            return evse.evse_id === evse_to_search;
          }
          return false;
        })?.status || "UNKNOWN"
      );
    }

    return "UNKNOWN";
  };

  useEffect(() => {
    let durationIntervalId: number;
    if (state === 2 || state === 3) {
      durationIntervalId = window.setInterval(() => {
        const currentTimeInSeconds = convertToSeconds(duration);
        const updatedTimeInSeconds = currentTimeInSeconds + 1;
        setDuration(convertToTimeStr(updatedTimeInSeconds));
        setWaitForResponseCounter(waitForResponseCounter + 1);
      }, 1000);
    }
    return () => {
      if (durationIntervalId) {
        clearInterval(durationIntervalId);
      }
    };
  }, [state, duration]);

  useEffect(() => {
    let intervalId: number;
    if (state == 2 || state == 3) {
      const paymentIntentId = searchParams.get("payment_intent");
      const fetchData = async () => {
        const response = await fetch(
          `${window.location.origin}/api/adhoc/status/charging`,
          {
            method: "POST",
            body: JSON.stringify({ paymentIntentId: paymentIntentId }),
          }
        );
        const payload = AdHocStatusChargingSchema.safeParse(
          await response.json()
        );
        if (payload.success) {
          setChargingData(payload.data.data);
          if (
            payload.data.data.status == StatusEnum.START_REJECTED ||
            payload.data.data.status == StatusEnum.REFUNDED
          ) {
            setState(4);
            clearInterval(intervalId);
          } else if (payload.data.data.status == StatusEnum.SESSION_RECEIVED) {
            if (paymentIntentId) {
              const selectedChargePointId =
                transformEvseToEvseSlugId(selectedEvse);
              router.push(
                `${window.location.origin}/qr/${selectedChargePointId}/status/${paymentIntentId}${langGetSuffix}`
              );
              router.refresh();
            }
          }
        }
      };
      intervalId = window.setInterval(() => {
        if (paymentIntentId) {
          void fetchData();
        }
      }, 30000); //all 30s
    }
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [state, chargePointId, router, searchParams, langGetSuffix]);

  const cancelAdhoc = async () => {
    const res = await fetch(
      `${window.location.origin}/api/adhoc/payment/cancel`,
      {
        method: "POST",
        body: JSON.stringify({
          paymentIntentId: searchParams.get("payment_intent"),
        }),
      }
    );
    const responseBody = (await res.json()) as ResponseBody;
    if (res.status == 200 && responseBody.status_code == ResponseCode.SUCCESS) {
      // Cancel was successful
      setState(6);
    } else {
      // Cancel was not successful
      setState(7);
    }
  };

  const onRadioChange = (e: ChangeEvent<HTMLInputElement>) => {
    setSelectedEvse(e.target.value);
  };

  const availableEvses = chargerLocation.evses.filter((evse) => {
    return evse.status == "AVAILABLE";
  });

  switch (state) {
    case 1:
      return (
        <div className={"flex h-full min-h-full flex-col justify-between"}>
          <div className={"flex-grow "}>
            <div className={"mt-5 text-center  text-2xl font-bold "}>
              {t("start_session")}
            </div>
            <div className={"mb-2 mt-5 text-2xl font-bold"}>
              {t("select_charger")}
            </div>
            <ul className={"flex flex-col gap-3 text-xl font-normal"}>
              <li className={"flex items-center"}>
                <label className={"flex items-center"}>
                  <>
                    <input
                      checked={selectedEvse == evse}
                      className={
                        "font-3xl text-grey-700 mr-3 inline-block h-7 w-7 items-center"
                      }
                      type="radio"
                      name="scannedEvse"
                      value={evse}
                      onChange={onRadioChange}
                    />
                    <BoldifyLastTwoChars str={evse} /> ({t("scanned")})
                  </>
                </label>
              </li>
              {selectedEvse != evse && (
                <li className={"flex items-center"}>
                  <label className={"flex items-center"}>
                    <input
                      checked={true}
                      onChange={onRadioChange}
                      className={
                        "font-3xl  text-grey-700 mr-3 h-7 w-7 items-center"
                      }
                      type="radio"
                      name="selectedEvse"
                      value={selectedEvse}
                    />
                    <BoldifyLastTwoChars str={selectedEvse} />
                  </label>
                </li>
              )}
            </ul>
            {availableEvses.length >= 1 && (
              <button
                onClick={() => setOpenChargerChooseBox(true)}
                className={
                  "mt-5 w-full  cursor-pointer rounded-xl bg-primary p-3  text-2xl font-bold text-secondary"
                }
              >
                {t("change_charger")}
              </button>
            )}
          </div>
          <div className={"flex-shrink items-end pb-5"}>
            <div className={"  "}>
              {["CHARGING", "UNKNOWN", "REMOVED"].includes(
                findStatusByEvse(chargerLocation, selectedEvse)
              ) ? (
                <div>
                  <div className={"mt-3 w-full text-xl "}>
                    {t("cannot_start_charger")}
                  </div>
                  <div className={"flex flex-row justify-between gap-5"}>
                    <div className={"mt-3 w-full text-xl"}>
                      <button
                        onClick={() => setOpenCancelModal(true)}
                        className={
                          "mt-5 w-full rounded-xl bg-red-400 p-3 text-2xl  font-bold  text-secondary"
                        }
                      >
                        {t("cancel")}
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <div
                  onClick={() => void startCharging()}
                  className={"w-full text-xl"}
                >
                  <button
                    className={
                      "mt-3 w-full  rounded-xl bg-primary p-3 text-2xl font-bold  text-secondary"
                    }
                  >
                    {t("start")}
                  </button>
                </div>
              )}
            </div>
          </div>
          {openChargerChooseBox && (
            <div
              className={`h-content absolute
            left-0  top-0 flex w-full transform flex-col rounded-t-3xl bg-white pb-8 transition-all duration-500 ease-in-out`}
            >
              <div
                className={
                  "flex h-full flex-col justify-between gap-5 overflow-y-auto px-6 text-2xl text-primary"
                }
              >
                <div
                  className={
                    "mt-10 flex  h-full max-h-[50vh] max-h-[70svh] flex-grow flex-col gap-5 overflow-y-scroll"
                  }
                >
                  <div className={"flex-shrink-0 font-sansPro font-bold"}>
                    {t("select_charger")}
                    <div
                      onClick={() => setOpenChargerChooseBox(false)}
                      className={"absolute right-5 top-10"}
                    >
                      <Image
                        alt={"close"}
                        src={"/commonImages/close.svg"}
                        className={"cursor-pointer"}
                        width={25}
                        height={25}
                      />
                    </div>
                  </div>
                  <ul
                    className={
                      "flex flex-grow flex-col gap-3 overflow-y-scroll "
                    }
                  >
                    {openChargerChooseBox &&
                      chargerLocation &&
                      chargerLocation.evses.map((evse) => {
                        if (
                          evse.status == "AVAILABLE" ||
                          evse.status == "PLANNED" ||
                          evse.status == "CHARGING"
                        ) {
                          return (
                            <li
                              key={evse.evse_id}
                              className={"flex items-center"}
                            >
                              <label className={"flex items-center"}>
                                <input
                                  disabled={evse.status == "CHARGING"}
                                  checked={
                                    evse.status != "CHARGING" &&
                                    selectedEvse == evse.evse_id
                                  }
                                  className={
                                    "font-3xl  text-grey-700 mr-3 h-7 w-7 items-center"
                                  }
                                  type="radio"
                                  name="evse"
                                  value={evse.evse_id || ""}
                                  onChange={onRadioChange}
                                />
                                <BoldifyLastTwoChars str={evse.evse_id} />{" "}
                                {evse.status == "CHARGING" && "(Belegt)"}
                              </label>
                            </li>
                          );
                        }
                      })}
                  </ul>
                </div>
              </div>

              <div className={" px-5"}>
                <button
                  onClick={() => setOpenChargerChooseBox(false)}
                  className={
                    "w-full rounded-xl bg-primary p-3 text-2xl font-bold text-secondary "
                  }
                >
                  {t("apply")}
                </button>
              </div>
            </div>
          )}
          <div
            className={`fixed left-0 ${
              openCancelModal ? "top-[150px]" : "top-screen"
            }  h-content flex w-full transform flex-col rounded-t-3xl bg-white pb-8 font-sansPro transition-all duration-500 ease-in-out`}
          >
            <div
              className={
                "flex h-full flex-col justify-between gap-5 overflow-y-auto px-6 text-2xl text-primary"
              }
            >
              <div
                className={
                  "mt-10 flex  h-full max-h-[50vh] max-h-[70svh] flex-grow flex-col gap-5"
                }
              >
                <div
                  className={
                    "flex-shrink-0 justify-items-start font-sansPro font-bold"
                  }
                >
                  {t("cancel_session")}
                  <div
                    onClick={() => setOpenCancelModal(false)}
                    className={"absolute right-5 top-10"}
                  >
                    <Image
                      alt={"close"}
                      src={"/commonImages/close.svg"}
                      width={25}
                      height={25}
                    />
                  </div>
                </div>
                <div className={"flex flex-col gap-3 text-2xl "}>
                  <Trans
                    i18nKey={"common:cancel_and_refund"}
                    components={[<br key={"cancel_refund"} />]}
                  />
                </div>
                <div className={"flex-shrink-0"}>
                  <div className={"flex flex-row justify-between gap-5"}>
                    <div className={"mt-3 w-full text-xl"}>
                      <button
                        onClick={() => void cancelAdhoc()}
                        className={
                          "mt-5 w-full rounded-xl bg-red-400 p-3 text-2xl font-bold  text-secondary"
                        }
                      >
                        {t("acknowledge")}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    case 2:
      return (
        <>
          <div className={"mt-5  text-center text-2xl font-bold "}>
            {t("start_session")}
          </div>
          <div className={"mt-8 flex w-full flex-col gap-5 font-sans text-xl"}>
            <div className={"text-3xl font-bold text-slate-800"}>
              {selectedEvse}
            </div>
            {waitForResponseCounter > 70 ? (
              <div className={"text-xl font-bold"}>
                {t("cable_was_plugged")}
              </div>
            ) : (
              <div className={"text-xl font-bold"}>
                {t("cable_already_plugged")}
              </div>
            )}

            {waitForResponseCounter <= 60 ? (
              <div className={"text-xl "}>{t("connecting")}.</div>
            ) : waitForResponseCounter > 70 ? (
              <div className={"text-xl "}>
                {t("start_session_failed_sorry")}
              </div>
            ) : (
              <div className={"text-xl "}>{t("longer_than_usual")}</div>
            )}
            {waitForResponseCounter <= 70 ? (
              <div
                className={"w-30 h-30 flex items-center justify-center pt-10"}
              >
                <div className={"loader items-center"}></div>
              </div>
            ) : (
              <div
                className={
                  "fixed bottom-5 left-0 flex w-full justify-center px-5"
                }
              >
                <Link
                  href={`${window.location.origin}/qr/${chargePointId}`}
                  className={"w-full"}
                >
                  <button
                    className={
                      "w-full rounded-xl bg-primary p-3 text-2xl font-bold  text-secondary"
                    }
                  >
                    {t("home")}
                  </button>
                </Link>
              </div>
            )}
          </div>
        </>
      );

    case 4:
      return (
        <>
          <div className={"mt-5 text-center  text-2xl font-bold "}>
            {t("unknown_error_short")}
          </div>
          <div>
            <div>{t("start_session_failed")}</div>
            <div
              className={
                "mt-2 w-full cursor-pointer rounded-xl bg-primary p-3 text-center  text-2xl text-secondary"
              }
              onClick={() => {
                router.push(
                  `${window.location.origin}/qr/${chargePointId}${langGetSuffix}`
                );
                router.refresh();
              }}
            >
              {t("start_session_again")}
            </div>
          </div>
        </>
      );
    case 5:
      return (
        <div>
          <div className={"mt-5 text-center  text-2xl font-bold "}>
            {t("start_session")}
          </div>

          <div>{t("thanks_for_charging")}</div>
        </div>
      );
    case 6: // cancel was successful
      return (
        <div>
          <div className={"mt-5 text-center  text-2xl font-bold "}>
            {t("start_session")}
          </div>
          <div className={"mt-5 text-left  text-base"}>
            <br />
            {t("cancel_successfully")}
          </div>
          <Link href={`${window.location.origin}/qr/${chargePointId}`}>
            <div className={"mt-5 text-xl"}>
              <button
                className={
                  "mt-3 w-full  rounded-xl bg-primary p-3 text-2xl font-bold  text-secondary"
                }
              >
                {t("back_home")}
              </button>
            </div>
          </Link>
        </div>
      );
    case 7: // cancel was not successful
      return (
        <div>
          <div className={"mt-5 text-center  text-2xl font-bold "}>
            {t("session_canceled")}
          </div>
          <div className={"mt-5  text-base"}>
            <br />
            {t("cancel_failed")}
          </div>
          <Link href={`${window.location.origin}/qr/${chargePointId}`}>
            <div className={"mt-5 text-xl"}>
              <button
                className={
                  "mt-3 w-full  rounded-xl bg-primary p-3 text-2xl font-bold  text-secondary"
                }
              >
                {t("back_home")}
              </button>
            </div>
          </Link>
        </div>
      );
    default:
      return (
        <div>
          <div
            className={
              "w-full rounded-xl bg-primary p-3 text-center  text-primary"
            }
            onClick={() => {
              router.push(
                `${window.location.origin}/qr/${chargePointId}${langGetSuffix}`
              );
              router.refresh();
            }}
          >
            {t("back_home")}
          </div>
        </div>
      );
  }
};

export default RenderState;
