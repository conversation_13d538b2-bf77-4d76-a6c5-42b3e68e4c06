import { z } from "zod";
import { createEnv } from "@t3-oss/env-nextjs";

export const env = createEnv({
  /**
   * Specify your server-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars.
   */
  server: {
    DATABASE_URL: z.string().url(),
    NODE_ENV: z.enum(["development", "test", "production"]),
    NEXTAUTH_SECRET:
      process.env.NODE_ENV === "production"
        ? z.string().min(1)
        : z.string().min(1).optional(),
    NEXTAUTH_URL: z.preprocess(
      // This makes Vercel deployments not fail if you don't set NEXTAUTH_URL
      // Since NextAuth.js automatically uses the VERCEL_URL if present.
      (str) => process.env.VERCEL_URL ?? str,
      // VERCEL_URL doesn't include `https` so it cant be validated as a URL
      process.env.VERCEL ? z.string().min(1) : z.string().url()
    ),
    // Add `.min(1) on ID and SECRET if you want to make sure they're not empty
    DISCORD_CLIENT_ID: z.string(),
    DISCORD_CLIENT_SECRET: z.string(),
    STRIPE_SECRET_KEY: z.string(),
    OCPI_DOMAIN: z.string(),
    LONGSHIP_DOMAIN: z.string(),
    CPO_TOKEN: z.string(),
    EMP_TOKEN: z.string(),
    INVOICE_FOLDER: z.string(),
    EMAIL_SERVER_USER: z.string(),
    EMAIL_SERVER_PASSWORD: z.string(),
    EMAIL_SERVER_HOST: z.string(),
    EMAIL_SERVER_PORT: z.string(),
    EMAIL_FROM: z.string(),
    CHAT_WEBHOOK: z.string(),
    MAIL_FOR_LOGGING_MESSAGES: z.string().optional(),
    DEBUG: z.string().optional().default("false"),
    LONGSHIP_API_OCP_KEY: z.string(),
    LONGSHIP_API_X_API_KEY: z.string(),
    LONGSHIP_API_URL: z.string(),
  },

  /**
   * Specify your client-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars. To expose them to the client, prefix them with
   * `NEXT_PUBLIC_`.
   */
  client: {
    // NEXT_PUBLIC_CLIENTVAR: z.string().min(1),
    NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: z.string(),
    NEXT_PUBLIC_DEBUG: z.string().optional().default("false"),
    NEXT_PUBLIC_SITE_URL: z.string(),
  },

  /**
   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.
   * middlewares) or client-side so we need to destruct manually.
   */
  runtimeEnv: {
    DATABASE_URL: process.env.DATABASE_URL,
    NODE_ENV: process.env.NODE_ENV,
    OCPI_DOMAIN: process.env.OCPI_DOMAIN,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    DISCORD_CLIENT_ID: process.env.DISCORD_CLIENT_ID,
    DISCORD_CLIENT_SECRET: process.env.DISCORD_CLIENT_SECRET,
    STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
    NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY:
      process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
    LONGSHIP_DOMAIN: process.env.LONGSHIP_DOMAIN,
    CPO_TOKEN: process.env.CPO_TOKEN,
    EMP_TOKEN: process.env.EMP_TOKEN,
    INVOICE_FOLDER: process.env.INVOICE_FOLDER,
    EMAIL_SERVER_USER: process.env.EMAIL_SERVER_USER,
    EMAIL_SERVER_PASSWORD: process.env.EMAIL_SERVER_PASSWORD,
    EMAIL_SERVER_HOST: process.env.EMAIL_SERVER_HOST,
    EMAIL_SERVER_PORT: process.env.EMAIL_SERVER_PORT,
    EMAIL_FROM: process.env.EMAIL_FROM,
    DEBUG: process.env.DEBUG,
    CHAT_WEBHOOK: process.env.CHAT_WEBHOOK,
    MAIL_FOR_LOGGING_MESSAGES: process.env.MAIL_FOR_LOGGING_MESSAGES,
    NEXT_PUBLIC_DEBUG: process.env.NEXT_PUBLIC_DEBUG,
    LONGSHIP_API_OCP_KEY: process.env.LONGSHIP_API_OCP_KEY,
    LONGSHIP_API_X_API_KEY: process.env.LONGSHIP_API_X_API_KEY,
    LONGSHIP_API_URL: process.env.LONGSHIP_API_URL,
    NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,
  },
});
