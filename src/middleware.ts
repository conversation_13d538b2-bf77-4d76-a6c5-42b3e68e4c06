import { type NextRequest, NextResponse } from "next/server";
import { env } from "~/env.mjs";
import { validateToken } from "~/utils/ocpiAuth/ocpiAuthUtil";
import { RateLimiterMemory } from "rate-limiter-flexible";
export const config = {
  matcher: ["/ocpi/:path*", "/api/:path*"],
};

const logHandler = async (request: NextRequest) => {
  try {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const body = await request.json();
    console.log(
      "token: ",
      request.headers.get("authorization"),
      " ",
      request.method,
      ": ",
      request.url,
      "Body: ",
      body
    );
  } catch {
    console.log(
      "token: ",
      request.headers.get("authorization"),
      " ",
      request.method,
      ": ",
      request.url
    );
  }
};

const rateLimiter = new RateLimiterMemory({
  points: 2, // Number of points
  duration: 2, // Per second
});

const middleware = async (request: NextRequest) => {
  if (env.DEBUG === "true") {
    await logHandler(request);
  }

  const { pathname: requestedUrl } = request.nextUrl;

  if (requestedUrl.startsWith("/api/invoice/")) {
    try {
      // Consume a point for each request
      await rateLimiter.consume("EULEOFTHEDAY");

      // If successful, proceed with the request
      return NextResponse.next();
    } catch (rateLimiterRes) {
      // If rate limit is exceeded, send a 429 response
      return new NextResponse(
        "Zu viele Anfragen - Bitte versuchen Sie es in 1 min erneut",
        { status: 429 }
      );
    }
  }

  // public endpoints
  if (
    requestedUrl.startsWith("/ocpi/credentials") ||
    requestedUrl.startsWith("/ocpi/versions") ||
    requestedUrl.startsWith("/api")
  ) {
    return NextResponse.next();
  } else if (requestedUrl.startsWith("/ocpi")) {
    if (validateToken(request)) {
      return NextResponse.next();
    }

    return NextResponse.json(
      {
        success: false,
        message: "authentication failed",
      },
      { status: 401 }
    );
  }

  // default return 401
  return NextResponse.json(
    {
      success: false,
      message: "authentication failed",
    },
    { status: 401 }
  );
};

export default middleware;
