@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
    .h-screen {
        height: 100vh;
        height: 100dvh;
    }
    .top-screen {
        top: 100vh;
        top: 100dvh;
    }
    .h-content {
        height: calc(100vh - 150px);
        height: calc(100dvh - 150px);
    }
}

@layer components {
    .btn {
        @apply w-full rounded-xl bg-elm-700 p-3  text-2xl font-bold  text-white;
    }

}

.loader {
    font-size: 10px;
    width: 1em;
    height: 1em;
    border-radius: 50%;
    position: relative;
    text-indent: -9999em;
    animation: mulShdSpin 1.1s infinite ease;
    transform: translateZ(0);
}
@keyframes mulShdSpin {
    0%,
    100% {
        box-shadow: 0em -2.6em 0em 0em #ffffff, 1.8em -1.8em 0 0em rgba(33,105,124, 0.2), 2.5em 0em 0 0em rgba(33,105,124, 0.2), 1.75em 1.75em 0 0em rgba(33,105,124, 0.2), 0em 2.5em 0 0em rgba(33,105,124, 0.2), -1.8em 1.8em 0 0em rgba(33,105,124, 0.2), -2.6em 0em 0 0em rgba(33,105,124, 0.5), -1.8em -1.8em 0 0em rgba(33,105,124, 0.7);
    }
    12.5% {
        box-shadow: 0em -2.6em 0em 0em rgba(33,105,124, 0.7), 1.8em -1.8em 0 0em #ffffff, 2.5em 0em 0 0em rgba(33,105,124, 0.2), 1.75em 1.75em 0 0em rgba(33,105,124, 0.2), 0em 2.5em 0 0em rgba(33,105,124, 0.2), -1.8em 1.8em 0 0em rgba(33,105,124, 0.2), -2.6em 0em 0 0em rgba(33,105,124, 0.2), -1.8em -1.8em 0 0em rgba(33,105,124, 0.5);
    }
    25% {
        box-shadow: 0em -2.6em 0em 0em rgba(33,105,124, 0.5), 1.8em -1.8em 0 0em rgba(33,105,124, 0.7), 2.5em 0em 0 0em #ffffff, 1.75em 1.75em 0 0em rgba(33,105,124, 0.2), 0em 2.5em 0 0em rgba(33,105,124, 0.2), -1.8em 1.8em 0 0em rgba(33,105,124, 0.2), -2.6em 0em 0 0em rgba(33,105,124, 0.2), -1.8em -1.8em 0 0em rgba(33,105,124, 0.2);
    }
    37.5% {
        box-shadow: 0em -2.6em 0em 0em rgba(33,105,124, 0.2), 1.8em -1.8em 0 0em rgba(33,105,124, 0.5), 2.5em 0em 0 0em rgba(33,105,124, 0.7), 1.75em 1.75em 0 0em #ffffff, 0em 2.5em 0 0em rgba(33,105,124, 0.2), -1.8em 1.8em 0 0em rgba(33,105,124, 0.2), -2.6em 0em 0 0em rgba(33,105,124, 0.2), -1.8em -1.8em 0 0em rgba(33,105,124, 0.2);
    }
    50% {
        box-shadow: 0em -2.6em 0em 0em rgba(33,105,124, 0.2), 1.8em -1.8em 0 0em rgba(33,105,124, 0.2), 2.5em 0em 0 0em rgba(33,105,124, 0.5), 1.75em 1.75em 0 0em rgba(33,105,124, 0.7), 0em 2.5em 0 0em #ffffff, -1.8em 1.8em 0 0em rgba(33,105,124, 0.2), -2.6em 0em 0 0em rgba(33,105,124, 0.2), -1.8em -1.8em 0 0em rgba(33,105,124, 0.2);
    }
    62.5% {
        box-shadow: 0em -2.6em 0em 0em rgba(33,105,124, 0.2), 1.8em -1.8em 0 0em rgba(33,105,124, 0.2), 2.5em 0em 0 0em rgba(33,105,124, 0.2), 1.75em 1.75em 0 0em rgba(33,105,124, 0.5), 0em 2.5em 0 0em rgba(33,105,124, 0.7), -1.8em 1.8em 0 0em #ffffff, -2.6em 0em 0 0em rgba(33,105,124, 0.2), -1.8em -1.8em 0 0em rgba(33,105,124, 0.2);
    }
    75% {
        box-shadow: 0em -2.6em 0em 0em rgba(33,105,124, 0.2), 1.8em -1.8em 0 0em rgba(33,105,124, 0.2), 2.5em 0em 0 0em rgba(33,105,124, 0.2), 1.75em 1.75em 0 0em rgba(33,105,124, 0.2), 0em 2.5em 0 0em rgba(33,105,124, 0.5), -1.8em 1.8em 0 0em rgba(33,105,124, 0.7), -2.6em 0em 0 0em #ffffff, -1.8em -1.8em 0 0em rgba(33,105,124, 0.2);
    }
    87.5% {
        box-shadow: 0em -2.6em 0em 0em rgba(33,105,124, 0.2), 1.8em -1.8em 0 0em rgba(33,105,124, 0.2), 2.5em 0em 0 0em rgba(33,105,124, 0.2), 1.75em 1.75em 0 0em rgba(33,105,124, 0.2), 0em 2.5em 0 0em rgba(33,105,124, 0.2), -1.8em 1.8em 0 0em rgba(33,105,124, 0.5), -2.6em 0em 0 0em rgba(33,105,124, 0.7), -1.8em -1.8em 0 0em #ffffff;
    }
}


.eyes {
    position: relative;
    width: 85px;
    display: flex;
    justify-content: space-between;
}
.eyes::after , .eyes::before  {
    content: '';
    display: inline-block;
    width: 35px;
    height: 35px;
    background-color: #FFF;
    background-image:  radial-gradient(circle 10px, #0d161b 100%, transparent 0);
    background-repeat: no-repeat;
    border-radius: 50%;
    animation: eyeMove 10s infinite , blink 10s infinite;
}
@keyframes eyeMove {
    0%  , 10% {     background-position: 0px 0px}
    13%  , 40% {     background-position: -12px 0px}
    43%  , 70% {     background-position: 12px 0px}
    73%  , 90% {     background-position: 0px 15px}
    93%  , 100% {     background-position: 0px 0px}
}
@keyframes blink {
    0%  , 10% , 12% , 20%, 22%, 40%, 42% , 60%, 62%,  70%, 72% , 90%, 92%, 98% , 100%
    { height: 35px}
    11% , 21% ,41% , 61% , 71% , 91% , 99%
    { height: 20px}
}
