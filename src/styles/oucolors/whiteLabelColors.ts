export interface Theme {
  background: string;
  logo: string;
  head?: string;
  hero: string;
  globe?: string;
  logoWidth?: number;
  logoHeight?: number;
}

export const whitelabelThemes: {
  [identifier: string]: Theme;
} = {
  default: {
    background: "/whitelabel/EUL/background.svg",
    logo: "/whitelabel/EUL/eulektro.svg",
    head: "/whitelabel/EUL/headOwl.svg",
    hero: "/whitelabel/EUL/hero.jpg",
  },
  EUL: {
    background: "/whitelabel/EUL/background.svg",
    logo: "/whitelabel/EUL/eulektro.svg",
    head: "/whitelabel/EUL/headOwl.svg",
    hero: "/whitelabel/EUL/hero.jpg",
  },
  MID: {
    background: "/whitelabel/MID/background.svg",
    logo: "/whitelabel/MID/midorion.png",
    hero: "/whitelabel/MID/hero_mid.jpg",
  },
  "1EC": {
    background: "/whitelabel/1EC/background.svg",
    logo: "/whitelabel/1EC/solarbewegt_logo.svg",
    hero: "/whitelabel/1EC/hero.png",
    globe: "/whitelabel/1EC/globe.svg",
  },
  "309": {
    background: "/whitelabel/309/background.svg",
    logo: "/whitelabel/309/ah_logo.svg",
    hero: "/whitelabel/309/hero.png",
    globe: "/whitelabel/309/globe.svg",
  },
  "129": {
    background: "/whitelabel/129/background.svg",
    logo: "/whitelabel/129/rm_logo.svg",
    hero: "/whitelabel/129/hero.png",
    globe: "/whitelabel/129/globe.svg",
  },
  "1ST": {
    background: "/whitelabel/1ST/background.svg",
    logo: "",
    hero: "/whitelabel/1ST/firstcharge_hero_mid.png",
    globe: "/whitelabel/1ST/globe.svg",
  },

  GFG: {
    background: "/whitelabel/GFG/background.svg",
    logo: "/whitelabel/GFG/GfG.svg",
    logoWidth: 210,
    logoHeight: 15,
    hero: "/whitelabel/GFG/Hero_GfG_780x422px.png",
    globe: "/whitelabel/GFG/globe.svg",
  },
};

export const getCpoOperatorId = (evseId: string) => {
  // nur zweites element, rest ignorieren
  const [, cpoOperatorId, stationId] = evseId.split("*");
  if (cpoOperatorId !== "EUL") {
    return cpoOperatorId;
  } else {
    const subId = stationId?.substring(1, 4);
    if (subId && whitelabelThemes[subId?.toUpperCase()]) {
      return subId?.toUpperCase();
    }
  }
  return cpoOperatorId ?? "EUL";
};

export const getWhitelabelRootByOperator = (cpoOperatorId: string): Theme => {
  const whiteLabelRoot = whitelabelThemes[cpoOperatorId];
  return whiteLabelRoot ?? (whitelabelThemes.default as Theme);
};

export const whiteLabelColors: {
  [identifier: string]: { [csstag: string]: string };
} = {
  EUL: {
    "--color-primary": "#214958",
    "--color-secondary": "#112f3b",
    "--color-main": "#e7e5e4",
    "--color-primary-text": "#21697C",
    "--color-secondary-text": "#ECECE7",
    "--bgurl": "url('/whitelabel/EUL/background.svg')",

    stripe: "#214958",
  },
  MID: {
    "--color-primary": "#89FEA1",
    "--color-secondary": "#17B86E",
    "--color-main": "#ebf5eb",
    "--color-primary-text": "#000000",
    "--color-secondary-text": "#000000",
    "--bgurl": "url('/whitelabel/MID/background.svg')",
    stripe: "#000000",
  },
  "1EC": {
    "--color-primary": "#f5d08e",
    "--color-secondary": "#ff9f00",
    "--color-main": "#ebf5eb",
    "--color-primary-text": "#ff9f00",
    "--color-secondary-text": "#000000",
    "--bgurl": "url('/whitelabel/1EC/background.svg')",
    stripe: "#000000",
  },
  "309": {
    "--color-primary": "#f5d08e",
    "--color-secondary": "#ff9f00",
    "--color-main": "#ebf5eb",
    "--color-primary-text": "#ff9f00",
    "--color-secondary-text": "#000000",
    "--bgurl": "url('/whitelabel/309/background.svg')",
    stripe: "#000000",
  },
  "129": {
    "--color-primary": "#f5d08e",
    "--color-secondary": "#ff9f00",
    "--color-main": "#ebf5eb",
    "--color-primary-text": "#ff9f00",
    "--color-secondary-text": "#000000",
    "--bgurl": "url('/whitelabel/129/background.svg')",
    stripe: "#000000",
  },
  "1ST": {
    "--color-primary": "#f7931e",
    "--color-secondary": "#65787f",
    "--color-main": "#A2ACAE",
    "--color-primary-text": "#000000",
    "--color-secondary-text": "#000000",
    "--bgurl": "url('/whitelabel/1ST/background.svg')",
    stripe: "#000000",
  },

  GFG: {
    "--color-primary": "#ffed00",
    "--color-secondary": "#ffffff",
    "--color-main": "#ffffff",
    "--color-primary-text": "#333333",
    "--color-secondary-text": "#333333",
    "--bgurl": "url('/whitelabel/GFG/background.svg')",
    stripe: "#000000",
  },

  Default: {
    "--color-primary": "#7c46c4",
    "--color-secondary": "#ECECE7",
    "--color-main": "#e7e5e4",
    "--color-primary-text": "#461d7c",
    "--color-secondary-text": "#e7e5e4",
    "--bgurl": "url('/whitelabel/default/background.svg')",
    stripe: "#461d7c",
  },

  // ... andere Themes
};
