import React from "react";
import NextLink, { type LinkProps } from "next/link";
import useTranslation from "next-translate/useTranslation";

interface LocaleLinkProps extends LinkProps, React.PropsWithChildren {
  className?: string;
}

const Link = ({
  children,
  href,
  className,
}: LocaleLinkProps): React.JSX.Element => {
  const { lang } = useTranslation("common");
  let modifiedHref;

  /*
  If href is a string, it can be manipulated directly.
  If href is not string it first needs to be extracted from the URL Object to be manipulated
  In both cases - if it starts with http it is external so don't modify it
   */
  if (typeof href === "string") {
    if (href.startsWith("http")) {
      modifiedHref = href;
    } else {
      modifiedHref = `${href}?lang=${lang}`;
    }
  } else if (typeof href === "object" && href !== null && href.href) {
    if (href.href.startsWith("http")) {
      modifiedHref = href;
    } else {
      modifiedHref = { ...href, href: `${href.href}?lang=${lang}` };
    }
  } else {
    throw new Error(`Invalid type for href: ${typeof href}`);
  }

  return (
    <NextLink className={className} href={modifiedHref}>
      {children}
    </NextLink>
  );
};

export default Link;
