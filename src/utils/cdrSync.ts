import { env } from "~/env.mjs";
import { CpoHeaders } from "~/utils/cpo/header";
import {
  SendInfoMessage,
  SendErrorMessage,
  SendDebugMessage,
} from "~/utils/chat/sendMessage";
import z from "zod";
import { CdrSchema } from "../../prisma/generated/zod";

interface SyncResult {
  totalProcessed: number;
  successful: number;
  failed: number;
  errors: string[];
}

/**
 * Hilfsfunktion für Verzögerung
 * @param ms Millisekunden zu warten
 */
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

const OCPICdrResponseSchema = z.object({
  data: z.array(CdrSchema),
  status_code: z.number(),
  status_message: z.string(),
  timestamp: z.string(),
});
type OCPICdrResponse = z.infer<typeof OCPICdrResponseSchema>;

const OCPIResponseSchema = z.object({
  status_code: z.number(),
  status_message: z.string(),
  timestamp: z.string(),
});

type OCPIResponse = z.infer<typeof OCPIResponseSchema>;

/**
 * Synchronisiert CDRs vom Backend für einen bestimmten Zeitraum und leitet sie an den lokalen POST-Endpunkt weiter
 * @param dateFrom Startdatum für die Synchronisierung
 * @param dateTo Enddatum für die Synchronisierung
 * @param offset Offset für die Paginierung (optional, Standard: 0)
 * @param limit Anzahl der Einträge pro Seite (optional, Standard: 100)
 * @returns Ergebnis der Synchronisierung
 */
export async function syncCdrsFromBackend(
  dateFrom: Date,
  dateTo: Date,
  offset = 0,
  limit = 100
): Promise<SyncResult> {
  const result: SyncResult = {
    totalProcessed: 0,
    successful: 0,
    failed: 0,
    errors: [],
  };

  try {
    const url = `${
      env.LONGSHIP_DOMAIN
    }/ocpi/2.2/cdrs/?date_from=${dateFrom.toISOString()}&date_to=${dateTo.toISOString()}&offset=${offset}&limit=${limit}`;

    await SendDebugMessage(`Fetching CDRs from: ${url}`);
    const response = await fetch(url, { headers: CpoHeaders });

    if (!response.ok) {
      const errorMsg = `Failed to fetch CDRs: ${response.statusText}`;
      result.errors.push(errorMsg);
      await SendErrorMessage(errorMsg);
      return result;
    }

    const responseData = (await response.json()) as OCPICdrResponse;

    // Prüfen, ob die Antwort das erwartete Format hat
    if (!responseData.data || !Array.isArray(responseData.data)) {
      const errorMsg = "Invalid response format from CDR endpoint";
      result.errors.push(errorMsg);
      await SendErrorMessage(errorMsg);
      return result;
    }

    // Für jedes CDR einen POST-Request an den lokalen Endpunkt senden
    for (const cdr of responseData.data) {
      result.totalProcessed++;

      try {
        await SendDebugMessage(
          `Processing CDR ${
            cdr.id
          }, pushing it to ${`${env.OCPI_DOMAIN}/ocpi/2.2/cdrs`}`
        );
        const postResponse = await fetch(`${env.OCPI_DOMAIN}/ocpi/2.2/cdrs`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Token ${env.EMP_TOKEN}`,
          },
          body: JSON.stringify(cdr),
        });

        const postResult = (await postResponse.json()) as OCPIResponse;

        if (postResponse.ok && postResult.status_code === 1000) {
          result.successful++;
          await SendDebugMessage(`Successfully processed CDR ${cdr.id}`);
        } else {
          result.failed++;
          const errorMsg = `Failed to process CDR ${cdr.id}: ${
            postResult.status_message
          } ${JSON.stringify(postResult)}`;
          result.errors.push(errorMsg);
          await SendInfoMessage(errorMsg);
        }

        // 0,5 Sekunden Verzögerung zwischen den Anfragen
        await delay(500);
      } catch (error: unknown) {
        result.failed++;
        const errorMsg = `Error processing CDR ${cdr.id}: ${String(error)}`;
        result.errors.push(errorMsg);
        await SendErrorMessage(errorMsg);

        // Auch bei Fehlern 0,5 Sekunden warten
        await delay(500);
      }
    }

    // Prüfen, ob weitere Seiten vorhanden sind
    const xTotalCount = response.headers.get("X-Total-Count");
    const xLimit = response.headers.get("X-Limit");

    if (xTotalCount && xLimit) {
      const totalCount = parseInt(xTotalCount, 10);
      const limitValue = parseInt(xLimit, 10);

      if (offset + limitValue < totalCount) {
        // Rekursiv die nächste Seite abrufen
        const nextPageResult = await syncCdrsFromBackend(
          dateFrom,
          dateTo,
          offset + limit,
          limit
        );

        // Ergebnisse zusammenführen
        result.totalProcessed += nextPageResult.totalProcessed;
        result.successful += nextPageResult.successful;
        result.failed += nextPageResult.failed;
        result.errors = [...result.errors, ...nextPageResult.errors];
      }
    }

    return result;
  } catch (error: unknown) {
    const errorMsg = `Error synchronizing CDRs: ${String(error)}`;
    result.errors.push(errorMsg);
    await SendErrorMessage(errorMsg);
    return result;
  }
}

/**
 * Synchronisiert einen einzelnen CDR anhand seiner ID
 * @param cdrId Die ID des zu synchronisierenden CDR
 * @returns Ergebnis der Synchronisierung
 */
export async function syncSingleCdrById(cdrId: string): Promise<SyncResult> {
  const result: SyncResult = {
    totalProcessed: 0,
    successful: 0,
    failed: 0,
    errors: [],
  };

  try {
    // Wir müssen einen Zeitraum der letzten 90 Tage verwenden, da die API keine direkte ID-Suche unterstützt
    const dateTo = new Date();
    const dateFrom = new Date();
    dateFrom.setDate(dateFrom.getDate() - 90); // 90 Tage zurück

    await SendDebugMessage(`Searching for CDR with ID: ${cdrId}`);

    // Wir suchen in Batches von 100, bis wir den CDR finden oder keine weiteren Ergebnisse mehr haben
    let found = false;
    let offset = 0;
    const limit = 100;
    let totalCount = 0;

    while (!found && (offset === 0 || offset < totalCount)) {
      const url = `${
        env.LONGSHIP_DOMAIN
      }/ocpi/2.2/cdrs/?date_from=${dateFrom.toISOString()}&date_to=${dateTo.toISOString()}&offset=${offset}&limit=${limit}`;

      await SendDebugMessage(`Fetching CDRs batch from: ${url}`);
      const response = await fetch(url, { headers: CpoHeaders });

      if (!response.ok) {
        const errorMsg = `Failed to fetch CDRs: ${response.statusText}`;
        result.errors.push(errorMsg);
        await SendErrorMessage(errorMsg);
        return result;
      }

      // Prüfen auf Paginierungsinformationen
      const xTotalCount = response.headers.get("X-Total-Count");
      if (xTotalCount) {
        totalCount = parseInt(xTotalCount, 10);
      }

      const responseData = (await response.json()) as OCPICdrResponse;

      if (!responseData.data || !Array.isArray(responseData.data)) {
        const errorMsg = "Invalid response format from CDR endpoint";
        result.errors.push(errorMsg);
        await SendErrorMessage(errorMsg);
        return result;
      }

      // Suche nach dem CDR mit der angegebenen ID
      const cdr = responseData.data.find((item) => item.id === cdrId);

      if (cdr) {
        found = true;
        result.totalProcessed = 1;

        try {
          await SendInfoMessage(
            `Found CDR with ID ${cdrId}, forwarding to local endpoint ${env.OCPI_DOMAIN}/ocpi/2.2/cdrs`
          );

          const postResponse = await fetch(`${env.OCPI_DOMAIN}/ocpi/2.2/cdrs`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Token ${env.EMP_TOKEN}`,
            },
            body: JSON.stringify(cdr),
          });

          const postResult = (await postResponse.json()) as OCPIResponse;

          if (postResponse.ok && postResult.status_code === 1000) {
            result.successful = 1;
            await SendDebugMessage(`Successfully processed CDR ${cdr.id}`);
          } else {
            result.failed = 1;
            const errorMsg = `Failed to process CDR ${cdr.id}: ${
              postResult.status_message
            } ${JSON.stringify(postResult)}`;
            result.errors.push(errorMsg);
            await SendInfoMessage(errorMsg);
          }
        } catch (error: unknown) {
          result.failed = 1;
          const errorMsg = `Catch: Error processing CDR ${cdr.id}: ${String(
            error
          )}`;
          result.errors.push(errorMsg);
          await SendErrorMessage(errorMsg);
        }

        break; // Wir haben den CDR gefunden und verarbeitet, also können wir die Schleife verlassen
      }

      // Wenn wir den CDR nicht gefunden haben und es weitere Ergebnisse gibt, erhöhen wir den Offset
      if (!found && responseData.data.length > 0) {
        offset += limit;
        await delay(500); // Kurze Pause zwischen den Anfragen
      } else if (responseData.data.length === 0) {
        break; // Keine weiteren Ergebnisse
      }
    }

    if (!found) {
      const errorMsg = `CDR with ID ${cdrId} not found in the last 90 days`;
      result.errors.push(errorMsg);
      await SendInfoMessage(errorMsg);
    }

    return result;
  } catch (error: unknown) {
    const errorMsg = `Error synchronizing CDR ${cdrId}: ${String(error)}`;
    result.errors.push(errorMsg);
    await SendErrorMessage(errorMsg);
    return result;
  }
}
