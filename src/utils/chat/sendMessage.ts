import { env } from "~/env.mjs";
import nodemailer from "nodemailer";
import { type ZodError } from "zod/lib/ZodError";
import { DateTime } from "luxon";

interface Props {
  zodError?: ZodError;
}

const MAX_MESSAGE_LENGTH = 1950;

export const SendErrorMessage = async (message: string, params?: Props) => {
  void (await sendMessage(`Error: ${message}`, params));
};

export const SendInfoMessage = async (message: string, params?: Props) => {
  void (await sendMessage(`Info: ${message}`, params));
};

export const SendDebugMessage = async (message: string, params?: Props) => {
  if (env.DEBUG == "true") {
    void (await sendMessage(`Debug: ${message}`, params));
  }
};

const sendMessage = async (message: string, params?: Props) => {
  const now = DateTime.now().setZone("Europe/Berlin").toISO();
  if (now) {
    message = `${now}\n\n${message}`;
  } else {
    message = `Error: ${message}`;
  }

  if (params && params.zodError) {
    message += `${params.zodError.message.replaceAll('"', "'")}`;
  }

  const splitMessageIntoChunks = (message: string): string[] => {
    const chunks = [];
    let start = 0;
    while (start < message.length) {
      const chunk = message.substring(start, start + MAX_MESSAGE_LENGTH);
      chunks.push(chunk);
      start += MAX_MESSAGE_LENGTH;
    }
    return chunks;
  };

  const chunks = splitMessageIntoChunks(message);
  for (const chunk of chunks) {
    try {
      const res = await fetch(env.CHAT_WEBHOOK, {
        method: "POST",
        body: `payload={"text": "${chunk}"}`,
      });
      console.log(env.CHAT_WEBHOOK, res, res.status, res.statusText);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch (e) {
      const transporter = nodemailer.createTransport({
        port: 465,
        host: env.EMAIL_SERVER_HOST,
        auth: {
          user: env.EMAIL_SERVER_USER,
          pass: env.EMAIL_SERVER_PASSWORD,
        },
        secure: true,
      });

      const invoiceMailData = {
        from: process.env.EMAIL_FROM,
        to:
            env.NODE_ENV == "production"
                ? "<EMAIL>"
                : env.MAIL_FOR_LOGGING_MESSAGES,
        replyTo: `<EMAIL>`,
        bcc: "<EMAIL>",
        subject: `Ad hoc Error on ${env.NODE_ENV}`,
        text: message,
      };

      transporter.sendMail(invoiceMailData, () => {
        return;
      });
    }
  }

};
