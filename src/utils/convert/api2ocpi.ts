export interface LongshipSession {
  id: string;
  tenantId: string;
  chargePointId: string;
  transactionId: number;
  ocppTransactionId: string;
  connectorId: number;
  sessionLocation: {
    id: string;
    evseId: string;
    powerType: string;
    country_code: string;
    party_id: string;
    name: string;
    houseNumber: string;
    street: string;
    city: string;
    postal_code: string;
    country: string;
    hotline_phonenumber: string;
    coordinates: {
      latitude: string;
      longitude: string;
    };
    time_zone: string;
    hasReimbursement: boolean;
  };
  idTag: string;
  startedByInfo: {
    tokenInfo: {
      uid: string;
      authReference: string;
      tokenType: string;
      contractId: string;
      authMethod: string;
      providerCountryCode: string;
      providerPartyId: string;
      tokenOUId: null;
      tokenOUName: null;
      tokenOU: null;
    };
    roamingPlatformType: string;
    authorizationState: string;
    roamingPlatformConnectionId: null;
    isGuestUsage: boolean;
  };
  meterStartInWh: number;
  sessionStart: string;
  chargingPeriods: any[];
  chargingMeterValues: any[];
  meterStopInWh: number | null;
  sessionStop: string | null;
  status: string;
  approvalStatus: number;
  reviewScenarioType: number;
  totalEnergyInKwh: number;
  totalPrice: number;
  created: string;
  lastUpdated: string;
  ou: string;
  ouId: string;
  ouName: string;
  tariffInfo: {
    tariffId: string;
    tariffName: string;
    startTariff: number;
    tariffPrice: number;
    parkingTariff: null;
    parkingStepSizeInMinutes: null;
    parkingGracePeriodInMinutes: null;
    timeTariff: null;
    timeStepSizeInMinutes: null;
    timeGracePeriodInMinutes: null;
    assertions: {
      tariffType: string;
      isTariffUsed: boolean;
      tariffResult: string;
    }[];
  };
  priceInfo: {
    startPrice: number;
    energyPrice: number;
    totalParkingTimeInMinutes: number;
    totalParkingTimeSteps: number;
    parkingTimePrice: number;
    totalChargingTimeInMinutes: number;
    totalChargingTimeSteps: number;
    chargingTimePrice: number;
    totalPrice: number;
  };
  tariffId: string;
  tariffName: string;
  startTariff: number;
  tariffPrice: number;
  parkingTariff: null;
  thresholds: null;
  parkingStepSize: null;
  delayInMinutes: null;
  parkingTimeStart: null;
}

export const convertSessionData = (original: LongshipSession) => {
  return {
    id: original.id,
    auth_method: original.startedByInfo.tokenInfo.authMethod,
    connector_id: original.connectorId.toString(),
    country_code: original.sessionLocation.country_code,
    currency: "EUR",
    evse_uid: "",
    kwh: original.totalEnergyInKwh,
    last_updated: original.lastUpdated,
    location_id: original.sessionLocation.id,
    party_id: original.sessionLocation.party_id,
    start_date_time: original.sessionStart,
    status: original.status,
    authorization_reference: original.startedByInfo.tokenInfo.authReference,
    cdr_token: {
      uid: original.startedByInfo.tokenInfo.uid,
      type: original.startedByInfo.tokenInfo.tokenType,
      contract_id: original.startedByInfo.tokenInfo.contractId,
    },
    charging_periods: [], // This should be mapped if necessary
    total_cost: {
      excl_vat: 0.0, // Assuming a value, as this needs to be calculated or provided
    },
    end_date_time: original.sessionStop,
  };
};
