import { prisma } from "~/server/db";
import { SendErrorMessage, SendInfoMessage } from "~/utils/chat/sendMessage";
import { CurrentType, type Session, StatusEnum } from "../../../prisma/client";
import { CpoHeaders, LongshipHeaders } from "~/utils/cpo/header";
import { ApiResponseSchema } from "~/app/ocpi/2.2/sessions/[...slug]/zod";
import { LongshipSession } from "~/utils/convert/api2ocpi";
import { env } from "~/env.mjs";
import { LocationResponseSchema } from "~/utils/schema/location";

export const getEmpByEvseId = async (evseId: string) => {
  const parts = evseId.split("*");

  const country_id = parts[0];
  const party_id = parts[1];

  const emp = await prisma.emp.findFirst({
    where: {
      party_id: party_id,
      country_code: country_id,
    },
  });
  if (!emp) {
    await SendInfoMessage(`EMP not found for evse: ${evseId}`);
    throw new Error("EMP not found");
  }

  return emp;
};
export const getCurrentType = (
  powerTypeFromConnector: string
): CurrentType | null => {
  if (powerTypeFromConnector.includes("AC")) {
    return CurrentType.AC;
  }
  if (powerTypeFromConnector.includes("DC")) {
    return CurrentType.DC;
  }
  return null;
};

export const getEvseIdByEvseUid = async (evse_uid: string) => {
  const locationWithEvses = await prisma.location.findFirst({
    where: {
      evses: {
        some: {
          uid: evse_uid,
        },
      },
    },
    select: {
      evses: true,
    },
  });
  const evse = locationWithEvses?.evses?.find((evse) => evse.uid == evse_uid);
  return evse?.evse_id;
};

export const getPriceForStationByEvseId = async (evseId: string) => {
  if (!evseId) {
    return {};
  }

  const location = await prisma.location.findFirst({
    where: {
      evses: {
        some: {
          evse_id: evseId,
        },
      },
    },
    select: { evses: true, id: true },
  });

  if (!location) {
    return {};
  }

  const evseObject = location?.evses?.find((evse) => evse.evse_id == evseId);

  if (!evseObject) {
    return {};
  }

  const powerTypeFromConnector = evseObject?.connectors[0]
    ?.power_type as string;
  const current_type = getCurrentType(powerTypeFromConnector);
  if (!current_type) {
    return {};
  }

  const locationPrice = await prisma.locationPrice.findFirst({
    where: {
      locationId: location.id,
      start: {
        lte: new Date(),
      },
      end: {
        gte: new Date(),
      },
      current_type: current_type,
    },
  });
  if (!locationPrice) {
    const emp = await getEmpByEvseId(evseId);
    if (!emp) {
      return {};
    }

    try {
      const empPrice = await prisma.empPrice.findFirst({
        where: {
          empId: emp.id,
          start: {
            lte: new Date(),
          },
          end: {
            gte: new Date(),
          },
          current_type: current_type,
        },
      });
      return { price: empPrice, current_type };
    } catch (e) {
      let message = "Exception in prisma.empPrice.findFirst";
      if (e instanceof Error) {
        message = `Exception in prisma.empPrice.findFirst ${e.message}`;
      }
      await SendErrorMessage(message);
    }
  }
  return { price: locationPrice, current_type };
};

export const fetchSessionFromBackend = async (
  url: string,
  session_id = "",
  authorization_reference = ""
): Promise<Session | null> => {
  const response = await fetch(url, { headers: CpoHeaders });
  const result = ApiResponseSchema.safeParse(await response.json());

  if (!result.success) {
    await SendInfoMessage(
      "utils/data/getBy.ts OCPI fetch API session result is not accepted",
      {
        zodError: result.error,
      }
    );
    throw new Error("OCPI fetch API session result is not accepted");
  }

  let session = null;
  if (session_id) {
    session =
      result.data.data.find((session) => session.id === session_id) ?? null;
  } else if (authorization_reference) {
    session =
      result.data.data.find(
        (session) => session.authorization_reference === authorization_reference
      ) ?? null;
  }

  if (session) {
    try {
      const paymentIntent = await prisma.paymentIntent.findFirst({
        where: { authorization_reference: session.authorization_reference },
      });
      if (paymentIntent) {
        void (await prisma.paymentIntent.update({
          where: {
            id: paymentIntent.id,
          },
          data: {
            status: StatusEnum.SESSION_RECEIVED,
          },
        }));
      }
    } catch (err: unknown) {
      await SendErrorMessage(
        `Updating paymentIntent status from restore route failed.`
      );
    }
    return session;
  } else {
    const xTotalCount = response.headers.get("X-Total-Count");
    const xLimit = response.headers.get("X-Limit");
    if (xTotalCount === null || xLimit === null) {
      await SendInfoMessage("No X-Total-Count or X-Limit header found");
      throw new Error("No X-Total-Count or X-Limit header found");
    }
    const totalCount = parseInt(xTotalCount, 10);
    const limit = parseInt(xLimit, 10);
    const currentOffset = parseInt(
      new URL(url).searchParams.get("offset") || "0",
      10
    );

    if (currentOffset + limit < totalCount) {
      const nextUrl = `${url}&offset=${currentOffset + limit}`;
      return await fetchSessionFromBackend(nextUrl, session_id);
    }

    return null; // Keine Session gefunden und keine weiteren Seiten zum Durchsuchen
  }
};

export const fetchSessionFromApiBackend = async (
  session_id = "",
  authorization_reference = ""
): Promise<LongshipSession | null> => {
  let session: LongshipSession | null = null;
  if (session_id) {
    const response = await fetch(
      `${env.LONGSHIP_API_URL}sessions/${session_id}`,
      {
        headers: LongshipHeaders,
      }
    );
    session = (await response.json()) as unknown as LongshipSession;
  } else if (authorization_reference) {
    const response = await fetch(
      `${env.LONGSHIP_API_URL}sessions?authorizationreferenceid=${authorization_reference}&limit=1`,
      {
        headers: LongshipHeaders,
      }
    );
    const responseData =
      (await response.json()) as unknown as LongshipSession[];
    if (responseData && Array.isArray(responseData) && responseData[0]) {
      session = responseData[0];
    }
  } else {
    return null;
  }

  if (session) {
    return session;
  }

  return null; // Keine Session gefunden und keine weiteren Seiten zum Durchsuchen
};
