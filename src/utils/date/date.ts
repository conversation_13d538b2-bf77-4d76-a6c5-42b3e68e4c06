import { DateTime } from "luxon";

export const getFirstDayOfMonth = (): Date => {
  const date = new Date();
  return new Date(date.getFullYear(), date.getMonth(), 1);
};

export const getLastDayOfMonth = (): Date => {
  const date = new Date();
  const lastDayOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);
  return new Date(
    lastDayOfMonth.getFullYear(),
    lastDayOfMonth.getMonth(),
    lastDayOfMonth.getDate(),
    23,
    59,
    59,
    999
  );
};

export const getFirstDayOfLastMonth = (): Date => {
  const date = new Date();
  const year =
    date.getMonth() === 0 ? date.getFullYear() - 1 : date.getFullYear();
  const month = date.getMonth() === 0 ? 11 : date.getMonth() - 1;
  return new Date(year, month, 1);
};

export const getLastDayOfLastMonth = (): Date => {
  const date = new Date();
  const year =
    date.getMonth() === 0 ? date.getFullYear() - 1 : date.getFullYear();
  const month = date.getMonth() === 0 ? 11 : date.getMonth() - 1;
  const lastDayOfMonth = new Date(year, month + 1, 0);
  return new Date(
    lastDayOfMonth.getFullYear(),
    lastDayOfMonth.getMonth(),
    lastDayOfMonth.getDate(),
    23,
    59,
    59,
    999
  );
};

export const convertUtcToCet = (utcDate: Date, locale = "de-DE"): string => {
  // CET ist entweder UTC+1 (Normalzeit) oder UTC+2 (Sommerzeit)
  const cetOffsetNormal = 60; // Minuten
  const cetOffsetDst = 120; // Minuten

  // Hilfsfunktion zum Prüfen der Sommerzeit (DST)
  function isDst(date: Date): boolean {
    const year = date.getFullYear();
    const lastSundayOfMarch = getLastSundayOfMonth(year, 2);
    const lastSundayOfOctober = getLastSundayOfMonth(year, 9);

    return date >= lastSundayOfMarch && date < lastSundayOfOctober;
  }

  // Hilfsfunktion zum Ermitteln des letzten Sonntags im Monat
  function getLastSundayOfMonth(year: number, month: number): Date {
    const lastDayOfMonth = new Date(year, month + 1, 0);
    const dayOfWeek = lastDayOfMonth.getDay();

    return new Date(year, month, lastDayOfMonth.getDate() - dayOfWeek);
  }

  // Ermitteln des richtigen CET-Offsets basierend auf der Sommerzeit
  const cetOffset = isDst(utcDate) ? cetOffsetDst : cetOffsetNormal;

  // Erstellen des CET-Datums
  const cetDate = new Date(utcDate);
  cetDate.setUTCMinutes(cetDate.getUTCMinutes() + cetOffset);

  // Formatieren des CET-Datums basierend auf der angegebenen Sprache (locale)
  const formattedCetDate = cetDate.toLocaleDateString(locale);

  return formattedCetDate;
};

export const startOfDay = (dateString: string) => {
  const date = new Date(dateString);
  date.setHours(0, 0, 0, 0);
  return date;
};

export const endOfDay = (dateString: string) => {
  const date = new Date(dateString);
  date.setHours(23, 59, 59, 999);
  return date;
};

export const formatDateRange = (date1: DateTime, date2: DateTime): string => {
  const sameDay = date1.hasSame(date2, "day");

  const formatDate = (date: DateTime): string =>
    date.toFormat("dd.MM.yyyy HH:mm");
  const formatTime = (date: DateTime): string => date.toFormat("HH:mm");

  if (sameDay) {
    return `am ${formatDate(date1)} Uhr bis ${formatTime(date2)} Uhr`;
  } else {
    return `vom ${formatDate(date1)} Uhr bis ${formatDate(date2)} Uhr`;
  }
};

// Hilfsfunktion zum Umwandeln von hh:mm:ss in Sekunden
export const convertToSeconds = (timeStr: string): number => {
  const parts = timeStr.split(":").map(Number);
  if (parts.length !== 3) {
    throw new Error("Invalid time format");
  }
  const hours = parts[0] ?? 0;
  const minutes = parts[1] ?? 0;
  const seconds = parts[2] ?? 0;

  return hours * 3600 + minutes * 60 + seconds;
};

// Hilfsfunktion zum Umwandeln von Sekunden in hh:mm:ss
export const convertToTimeStr = (totalSeconds: number): string => {
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(
    2,
    "0"
  )}:${String(seconds).padStart(2, "0")}`;
};
