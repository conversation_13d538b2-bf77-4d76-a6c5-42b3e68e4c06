import { type FC } from "react";

interface BoldifyLastTwoCharsProps {
  str: string | null;
}

const BoldifyLastTwoChars: FC<BoldifyLastTwoCharsProps> = ({ str }) => {
  if (!str) return null;
  const normalPart = str.slice(0, -2);
  const boldPart = str.slice(-2);

  return (
    <>
      {normalPart}
      <span className="font-bold">{boldPart}</span>
    </>
  );
};

export default BoldifyLastTwoChars;
