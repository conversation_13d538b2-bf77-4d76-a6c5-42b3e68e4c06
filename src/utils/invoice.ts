import {
  InvoiceStatusEnum,
  KindOfFile,
  Prisma,
  StatusEnum,
} from "../../prisma/client";
import { InvoicePdf } from "~/utils/invoicePdf";
import JsonValue = Prisma.JsonValue;
import { sendInvoice } from "~/utils/sendInvoice/sendInvoice";

import { prisma } from "~/server/db";

interface InvoiceProps {
  emp_country_code: string;
  emp_party_id: string;
  subject?: string;
  mailToSend?: string;
  service_period_from: Date;
  service_period_to: Date;
  local_start_datetime: string;
  local_end_datetime: string;
  authorization_reference: string;
  metadata?: JsonValue;
}

type ItemProps = {
  title: string;
  unit: string; // Unit of the item (e.g. kWh)
  amount: number; // Number of units
  description?: string;
  tax_rate: number;
} & (
  | {
      unit_price: number; // Price per unit
      unit_price_gross?: never; // Ensure unit_price_gross is never defined when unit_price is defined
    }
  | {
      unit_price?: never; // Ensure unit_price is never defined when unit_price_gross is defined
      unit_price_gross: number; // Price per unit including tax
    }
);

function roundNumber(num: number): number {
  return Number(num.toFixed(2));
}

function getCurrentDayOfYear() {
  const now = new Date();
  const startOfYear = new Date(now.getFullYear(), 0, 1);
  const millisecondsPerDay = 1000 * 60 * 60 * 24;
  const diffInMilliseconds = now.valueOf() - startOfYear.valueOf();
  return Math.floor(diffInMilliseconds / millisecondsPerDay) + 1;
}

export class InvoiceManager {
  async createInvoice(invoice: InvoiceProps) {
    const newInvoice = await prisma.invoice.create({
      data: {
        sentAsEmail: false,
        kind_of_invoice: "invoice",
        status: InvoiceStatusEnum.DRAFT,
        currency: "eur",
        invoice_date: new Date(),
        sum_tax: 0,
        sum_net: 0,
        sum_gross: 0,
        invoicePositions: [],
        ...invoice,
      },
    });
    return newInvoice;
  }

  async calcPositionsSum(invoiceId: string) {
    const invoice = await prisma.invoice.findUnique({
      where: {
        id: invoiceId,
      },
    });

    if (invoice) {
      let sumGross = 0;
      let sumNet = 0;
      let sumTax = 0;

      invoice.invoicePositions.forEach((item) => {
        // Check if unit_price, amount, and tax_rate are not null before calculating sums
        if (item.unit_price_gross && item.amount && item.tax_rate) {
          const itemGross = item.unit_price_gross * item.amount;
          sumGross += itemGross;
        }
      });
      sumNet += (sumGross / 119) * 100; // ToDo handle taxes better
      sumTax += sumNet * 0.19; // ToDo handle taxes better

      return prisma.invoice.update({
        where: {
          id: invoiceId,
        },
        data: {
          sum_gross: roundNumber(sumGross),
          sum_net: roundNumber(sumNet),
          sum_tax: roundNumber(sumTax),
        },
      });
    } else {
      return false;
    }
  }

  async addInvoiceItem(invoiceId: string, item: ItemProps) {
    const newInvoice = await prisma.invoice.findUnique({
      where: {
        id: invoiceId,
      },
      include: {
        invoicePositions: true, // Include invoicePositions in the response
      },
    });

    if (newInvoice) {
      const currentPosition = newInvoice.invoicePositions.length; // Get the current number of positions
      const newPosition = currentPosition + 1; // Set the new position

      let newItem;
      if (item.unit_price != undefined) {
        const sum_gross = item.unit_price * item.amount;
        const sum_tax = (item.tax_rate / 100) * sum_gross;
        const sum_net = sum_gross - sum_tax;
        const unit_price_gross = item.unit_price * (1 + item.tax_rate / 100);
        newItem = {
          ...item,
          position: newPosition,
          sum_gross: roundNumber(sum_gross),
          sum_net: roundNumber(sum_net),
          sum_tax: roundNumber(sum_tax),
          unit_price_gross: roundNumber(unit_price_gross),
        };
      } else {
        const unit_price = item.unit_price_gross / (1 + item.tax_rate / 100);
        const sum_gross = item.unit_price_gross * item.amount;
        const sum_net = sum_gross / (1 + item.tax_rate / 100);
        const sum_tax = sum_gross - sum_net;
        newItem = {
          ...item,
          position: newPosition,
          sum_gross: roundNumber(sum_gross),
          sum_net: roundNumber(sum_net),
          sum_tax: roundNumber(sum_tax),
          unit_price: roundNumber(unit_price),
        };
      }

      void (await prisma.invoice.update({
        where: {
          id: invoiceId,
        },
        data: {
          invoicePositions: {
            push: newItem,
          },
        },
      }));
      await this.calcPositionsSum(invoiceId);
    }

    return true;
  }

  async getNextSequenceValue(sequenceName: string): Promise<number> {
    const sequenceDocument = await prisma.counter.findUnique({
      where: { id: sequenceName },
    });

    if (sequenceDocument) {
      const updatedSequenceDocument = await prisma.counter.update({
        where: { id: sequenceName },
        data: { sequence_value: sequenceDocument.sequence_value + 1 },
      });

      return updatedSequenceDocument.sequence_value;
    }

    // Create a new counter document if it doesn't exist
    await prisma.counter.create({
      data: {
        id: sequenceName,
        sequence_value: 1,
      },
    });

    return 1;
  }

  async generateInvoiceNumber(): Promise<string> {
    const prefix = "AD_HOC_DE_EUL"; // ToDo make prefix dependent on emp
    const year = new Date().getFullYear();
    const dayOfYear = getCurrentDayOfYear();

    const dailyInvoiceNumber = await this.getNextSequenceValue(
      `${prefix}_${year}_${dayOfYear}`
    );

    return `${prefix}_${year}_${dayOfYear}_${String(
      dailyInvoiceNumber
    ).padStart(3, "0")}`;
  }

  async finalize(invoiceId: string) {
    const invoiceNumber = await this.generateInvoiceNumber();

    const updatedInvoice = await prisma.invoice.update({
      where: {
        id: invoiceId,
      },
      data: {
        status: InvoiceStatusEnum.INMUTABLE_WRITTEN,
        invoice_number: invoiceNumber, // Assuming invoice number is same as invoiceId, change as needed
      },
    });

    const invoicePdf = new InvoicePdf(updatedInvoice);
    await invoicePdf.init();

    void (await prisma.invoice.update({
      where: {
        id: invoiceId,
      },
      data: {
        files: {
          push: [
            {
              name: `${invoiceNumber}.pdf`,
              application_type: "application/pdf",
              path: invoicePdf.filepath,
              kind_of_file: KindOfFile.Invoice,
            },
          ],
        },
      },
    }));

    // Es soll ein PDF generiert werden und als Datei gespeichert werden.
    return true;
  }

  async setStatusToPaid(invoiceId: string) {
    const updatedInvoice = await prisma.invoice.update({
      where: {
        id: invoiceId,
      },
      data: {
        status: InvoiceStatusEnum.PAID,
        paid_date: new Date(),
      },
    });
    return updatedInvoice;
  }

  async send(invoiceId: string, paymentIntentId: string) {
    const invoice = await prisma.invoice.findUnique({
      where: {
        id: invoiceId,
      },
    });
    if (invoice && invoice.mailToSend) {
      const mailSent = sendInvoice(invoice);
      if (mailSent) {
        await prisma.paymentIntent.update({
          where: {
            id: paymentIntentId,
          },
          data: { status: StatusEnum.INVOICE_SENT },
        });
      }
    } else {
      return false;
    }

    return true;
  }
}
