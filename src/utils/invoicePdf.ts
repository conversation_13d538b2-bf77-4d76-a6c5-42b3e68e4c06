import { type Invoice } from "../../prisma/client";
import fs from "fs";
import { access } from "fs/promises";
import PDFDocument from "pdfkit";
import { env } from "~/env.mjs";
import { z } from "zod";
import { formatDateRange } from "~/utils/date/date";
import { prisma } from "~/server/db";
import * as path from "path";
import { DateTime } from "luxon";

export const InvoiceSchema = z.object({
  subject: z.string(),
  invoice_number: z.string(),
  invoice_date: z.date(),
  sum_gross: z.number(),
  sum_net: z.number(),
  sum_tax: z.number(),
  service_period_from: z.date(),
  service_period_to: z.date(),
  local_start_datetime: z.string(),
  local_end_datetime: z.string(),
  kind_of_invoice: z.string(),
  invoicePositions: z.array(
    z.object({
      position: z.number(),
      title: z.string(),
      description: z.string().optional(),
      unit: z.string(),
      unit_price: z.number(),
      unit_price_gross: z.number(),
      amount: z.number(),
      tax_rate: z.number(),
      sum_net: z.number(),
      sum_tax: z.number(),
      sum_gross: z.number(),
    })
  ),
});

export class InvoicePdf {
  invoice;

  #pdfPath: string;

  constructor(invoice: Invoice) {
    if (!invoice) {
      throw new Error("No invoice found");
    }
    const validateInvoice = InvoiceSchema.safeParse(invoice);
    if (!validateInvoice.success) {
      throw new Error("Invalid invoice");
    }
    this.invoice = validateInvoice.data;
    this.#pdfPath = "";
  }

  async init() {
    await this.createPdf();
  }

  get filepath(): string {
    return this.#pdfPath;
  }

  addItem() {
    return;
  }

  write() {
    return;
  }

  read() {
    return;
  }

  async fileExists(filename: string) {
    try {
      await access(filename);
      return true;
    } catch (err: unknown) {
      throw err;
    }
  }

  getCustomer() {
    return "<EMAIL>";
  }

  async getEmp() {
    const emp = await prisma.emp.findFirst({
      where: {
        country_code: "DE",
        party_id: "EUL",
      },
      include: {
        address: true,
      },
    });
    if (!emp) {
      throw new Error("No Emp found");
    }
    return emp;
  }

  async getEmpAdressByDate(date: Date) {
    const empAddress = await prisma.empAddress.findFirst({
      where: {
        AND: [
          {
            from: {
              lte: date,
            },
          },
          {
            to: {
              gte: date,
            },
          },
        ],
      },
    });

    if (!empAddress) {
      throw new Error("No EmpAddress found");
    }
    return empAddress;
  }

  get Subject(): string {
    return this.invoice.subject;
  }

  get Subtitle(): string {
    const localStartDate = DateTime.fromISO(this.invoice.local_start_datetime);
    const localEndDate = DateTime.fromISO(this.invoice.local_end_datetime);

    return `am ${this.invoice.local_start_datetime} Uhr bis ${this.invoice.local_end_datetime} Uhr`;
  }

  get InvoiceNumber(): string {
    return this.invoice.invoice_number || "";
  }

  get Customernumber(): string {
    return "";
  }

  get ServicePeriod(): string {
    if (!this.invoice) {
      throw new Error("No invoice found");
    }

    if (
      !this.invoice.local_start_datetime ||
      !this.invoice.local_end_datetime
    ) {
      throw new Error("No service period found");
    }
    // split 02.09.2024, 17:00 to get the date
    const from = this.invoice.local_start_datetime.split(",")[0];
    const to = this.invoice.local_end_datetime.split(",")[0];
    return `${from ?? ""} - ${to ?? ""}`;
  }

  get InvoiceDate(): string {
    if (this.invoice.invoice_date) {
      return this.formatDate(new Date(this.invoice.invoice_date));
    }
    return "";
  }

  padTo2Digits = (num: number) => {
    return num.toString().padStart(2, "0");
  };

  formatDate = (date: Date) => {
    return [
      this.padTo2Digits(date.getDate()),
      this.padTo2Digits(date.getMonth() + 1),
      date.getFullYear(),
    ].join(".");
  };

  drawTableHeader(doc: typeof PDFDocument, y: number) {
    doc.y = y;
    doc.rect(48, y, 517, 18).fillOpacity(0.25).fill("grey");
    doc.y += 2;
    doc.fillColor("black").fillOpacity(1);
    doc.font("public/font/OpenSans/OpenSans-Bold.ttf");
    doc.text("Pos.", 50, doc.y, {});
    doc.moveUp();
    doc.text("Beschreibung", 80, doc.y, {});
    doc.moveUp();
    doc.text("Menge", 290, doc.y, {});
    doc.moveUp();
    doc.text("Einheit", 340, doc.y, {});
    doc.moveUp();
    doc.text("MwSt.", 390, doc.y, {});
    doc.moveUp();
    doc.text("Einzelpreis", 435, doc.y, { width: 55 });
    doc.moveUp();
    doc.text("Gesamtpreis", 499, doc.y, { lineBreak: false });
  }

  drawFooterAndLogo(doc: typeof PDFDocument) {
    // Füge Logo hinzu
    doc.image("public/logo/EULEKTRO_21697c_R33_G105_B124.png", 36, 0, {
      height: 110,
    });

    // create Footer
    doc
      .moveTo(50, 785)
      .lineTo(560, 785)
      .lineWidth(0.5)
      .strokeColor("grey")
      .stroke();
    doc.fillColor("black").fillOpacity(1);
    doc.moveDown();
    doc.font("public/font/OpenSans/OpenSans-Bold.ttf").fontSize(6);
    doc.text("Eulektro GmbH", 50, 790, { lineBreak: false });
    doc.text("Kontakt", 200, 790, { lineBreak: false });
    doc.text("Bankverbindung", 350, 790, { lineBreak: false });
    doc.font("public/font/OpenSans/OpenSans-Regular.ttf");

    doc.moveDown();
    doc.text("Werderstraße 69", 50, 798, { lineBreak: false });
    doc.text("28199 Bremen", 50, 806, { lineBreak: false });
    doc.text("USt-ID: DE343815692", 50, 814, { lineBreak: false });
    doc.text("Geschäftsführer: Jan Runkel", 50, 822, { lineBreak: false });
    doc.text("Handelsregisternr.: HRB 36822 HB", 50, 830, { lineBreak: false });
    doc.text("E-Mail: <EMAIL>", 200, 798, { lineBreak: false });
    doc.text("Website: eulektro.de", 200, 806, { lineBreak: false });
    doc.text("Bank: Olinda Zweigniederlassung Deutschland", 350, 798, {
      lineBreak: false,
    });
    doc.text("IBAN: DE41 1001 0123 3550 1278 32", 350, 806, {
      lineBreak: false,
    });
    doc.text("BIC: QNTO DEB2 XXX", 350, 814, { lineBreak: false });
  }

  writePageNumber = (doc: typeof PDFDocument) => {
    //Global Edits to All Pages (Header/Footer, etc)
    const pages = doc.bufferedPageRange();
    for (let i = 0; i < pages.count; i++) {
      doc.switchToPage(i);

      //Footer: Add page number
      const oldBottomMargin = doc.page.margins.bottom;
      doc.page.margins.bottom = 0; //Dumb: Have to remove bottom margin in order to write into it
      doc.fontSize(8);
      doc.text(`Seite: ${i + 1} von ${pages.count}`, 500, 790, {
        lineBreak: false,
      });
      doc.page.margins.bottom = oldBottomMargin; // ReProtect bottom margin
    }
  };

  async createPdf() {
    // Erstelle ein neues PDF-Dokument
    const doc = new PDFDocument({
      bufferPages: true,
      size: "A4",
      font: "public/font/OpenSans/OpenSans-Regular.ttf",
    });
    this.drawFooterAndLogo(doc);

    // Füge Logo hinzu
    doc.image("public/logo/EULEKTRO_21697c_R33_G105_B124.png", 36, 0, {
      height: 110,
    });

    // Füge den Rechnungstitel hinzu
    doc
      .font("public/font/OpenSans/OpenSans-Regular.ttf")
      .fontSize(18)
      .text(`Rechnung`, 260, 135, {
        width: 300,
        lineBreak: false,
        align: "right",
      });

    // Setze den Schriftstil und die Schriftgröße
    doc.font("public/font/OpenSans/OpenSans-Regular.ttf").fontSize(12);

    // Füge Eulektro-Adresse hinzu
    doc
      .font("public/font/OpenSans/OpenSans-Bold.ttf")
      .fontSize(8)
      .text("Eulektro GmbH", 50, 155, { align: "left", continued: true });
    doc
      .font("public/font/OpenSans/OpenSans-Regular.ttf")
      .fontSize(8)
      .text(" | Werderstraße 69 | 28199 Bremen");

    //const customer = this.getCustomer();
    // Füge Customer-Adresse hinzu
    //doc.moveDown();
    //doc.fontSize(10).text(`${customer}`);
    //doc.moveDown();

    // Füge Rechnungsdetails hinzu
    doc.fontSize(9);

    doc.font("public/font/OpenSans/OpenSans-Bold.ttf");
    doc.y = 350;
    doc.text(`Betreff `, 50, doc.y);

    doc.y = 180;
    doc.text(`Rechnungsnummer: `, 320, doc.y, { align: "left", width: 120 });
    if (this.Customernumber) {
      doc.text(`Kundennummer: `, { align: "left", width: 120 });
    }
    doc.text(`Leistungszeitraum: `, { align: "left", width: 120 });
    doc.text(`Rechnungsdatum: `, { align: "left", width: 120 });

    doc.y = 350;
    doc.moveDown();
    // 95
    doc.font("public/font/OpenSans/OpenSans-Regular.ttf");
    doc.text(`${this.Subject} `, 50, doc.y, { width: 400 });
    doc.text(`${this.Subtitle}`, 50, doc.y, { width: 400 });

    doc.y = 180;
    doc.x = 428;
    doc.fontSize(9);
    doc.text(`${this.InvoiceNumber} `, 428, doc.y, {
      align: "left",
      lineBreak: false,
    });
    if (this.Customernumber) {
      doc.moveDown();
      doc.text(`${this.Customernumber} `, 428, doc.y, {
        align: "left",
        lineBreak: false,
      });
    }
    doc.moveDown();
    doc.text(`${this.ServicePeriod} `, 428, doc.y, {
      align: "left",
      lineBreak: false,
    });
    doc.moveDown();
    doc.text(`${this.InvoiceDate} `, 428, doc.y, {
      align: "left",
      lineBreak: false,
    });
    doc.moveDown();
    // Füge eine Tabelle mit den Rechnungspositionen hinzu

    const maxPositionsFirstPage = 6; // 7 passen auf Seite 1
    const maxPositionsPerPage = 14; // 12 auf folgenden Seiten Passen Sie diese Zahl an, um die Anzahl der Positionen pro Seite zu steuern.
    const headerAndFooterHeight = 100; // Passen Sie diese Zahl an, um den Platz für Kopf- und Fußzeile zu steuern.

    this.drawTableHeader(doc, 400);

    doc.moveDown();
    doc.moveDown();
    const regexDot = /\./;
    this.invoice.invoicePositions.forEach((position, index) => {
      // Überprüfen Sie, ob ein Seitenumbruch erforderlich ist

      if (
        (index !== 0 &&
          index <= maxPositionsFirstPage &&
          index % maxPositionsFirstPage === 0) ||
        (index > maxPositionsFirstPage &&
          (index - maxPositionsFirstPage) % maxPositionsPerPage === 0)
      ) {
        doc.addPage();
        this.drawFooterAndLogo(doc);
        doc.fontSize(10);
        this.drawTableHeader(doc, headerAndFooterHeight);
        doc.y = headerAndFooterHeight;
        doc.moveDown();
        doc.moveDown();
      }

      doc
        .font("public/font/OpenSans/OpenSans-Bold.ttf")
        .text(`${index + 1}`, 50, doc.y, {});
      doc.moveUp();
      if (position.description) {
        doc
          .font("public/font/OpenSans/OpenSans-Regular.ttf")
          .text(`${position.description}`, 80, doc.y, {
            width: 200,
            lineBreak: true,
          });
        doc.moveUp();
      }
      // Menge hinzufügen
      doc.text(
        `${position.amount.toFixed(2).replace(regexDot, ",")}`,
        290,
        doc.y,
        { width: 40, align: "right", lineBreak: false }
      );
      doc.moveUp();
      doc.text(`${position.unit}`, 340, doc.y);
      doc.moveUp();
      // MwSt. einfügen
      doc.text(`${position.tax_rate}%`, 390, doc.y, {
        width: 22,
        align: "right",
        lineBreak: false,
      });
      doc.moveUp();
      // Einzelpreis hinzufügen
      doc.text(
        `${position.unit_price_gross.toFixed(2).replace(regexDot, ",")}`,
        435,
        doc.y,
        { width: 55, align: "right", lineBreak: false }
      );
      doc.moveUp();

      // Gesamtpreis hinzufügen
      doc.text(
        `€ ${position.sum_gross.toFixed(2).replace(regexDot, ",")}`,
        504,
        doc.y,
        {
          width: 58,
          align: "right",
          lineBreak: false,
          lineGap: 5,
        }
      );
    });

    doc.moveDown();
    doc.moveDown();
    doc
      .moveTo(50, doc.y)
      .lineTo(563, doc.y)
      .lineWidth(0.5)
      .strokeColor("grey")
      .stroke();
    doc.moveUp();
    // Netto hinzufügen
    doc.text("Netto", 380, doc.y);
    doc.moveUp();
    doc.text(
      `€ ${this.invoice.sum_net.toFixed(2).replace(regexDot, ",")}`,
      450,
      doc.y,
      {
        align: "right",
        width: 112,
        lineBreak: false,
      }
    );
    // MwSt. hinzufügen // ToDo dynamic tax rate
    doc.text(`zzgl. 19% MwSt.`, 335, doc.y);
    doc.moveUp();
    doc.text(
      `€ ${this.invoice.sum_tax.toFixed(2).replace(regexDot, ",")}`,
      450,
      doc.y,
      {
        align: "right",
        width: 112,
        lineBreak: false,
      }
    );
    // Gesamtpreis hinzufügen
    doc
      .font("public/font/OpenSans/OpenSans-Bold.ttf")
      .text("Gesamtpreis", 344, doc.y, {
        lineBreak: false,
      });

    doc.text(
      `€ ${this.invoice.sum_gross.toFixed(2).replace(regexDot, ",")}`,
      344,
      doc.y,
      { align: "right", width: 218, lineBreak: false }
    );
    doc.moveDown();
    doc.moveDown();
    switch (this.invoice.kind_of_invoice) {
      case "invoice": {
        doc
          .font("public/font/OpenSans/OpenSans-Regular.ttf")
          .text("Vielen Dank für ihren Besuch.", 50, doc.y, { align: "left" });
        doc.moveDown();
        doc.moveDown();
        doc.moveDown();
        doc.moveDown();
        doc.moveDown();
        doc
          .font("public/font/OpenSans/OpenSans-Bold.ttf")
          .text("Diese Rechnung wurde bereits bezahlt.", 50, doc.y, {
            align: "left",
          });
        break;
      }
      case "cancel": {
        doc
          .font("public/font/OpenSans/OpenSans-Regular.ttf")
          .text(`Diese Stornorechnung storniert Rechnung`, 50, doc.y, {
            align: "left",
          });
        break;
      }
    }
    doc.moveDown();
    doc.moveDown();

    this.writePageNumber(doc);

    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth() + 1; // JavaScript Monate sind 0-basiert, also addieren wir 1

    // Ordnerpfad aufbauen
    const dir = path.join(env.INVOICE_FOLDER, String(year), String(month));

    // Überprüfen, ob der Ordner existiert, wenn nicht, dann erstellen
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true }); // Option "recursive" erlaubt die Erstellung von verschachtelten Ordnern
    }

    const fileName = `Invoice_${this.invoice.invoice_number}.pdf`;
    const pdfPath = path.join(dir, fileName);

    const stream = fs.createWriteStream(pdfPath);
    doc.pipe(stream);
    doc.end();

    await new Promise<void>((resolve) => {
      stream.on("finish", function () {
        resolve();
      });
    });
    this.#pdfPath = pdfPath;
    return pdfPath;
  }
}
