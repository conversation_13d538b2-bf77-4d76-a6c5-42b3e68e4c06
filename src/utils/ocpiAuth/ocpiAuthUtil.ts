import { type NextRequest } from "next/server";
import { env } from "~/env.mjs";

export const validateToken = (
  request: NextRequest,
  expectedToken: string = env.EMP_TOKEN
): boolean => {
  const tokenWithPrefixFromRequest = request.headers.get("authorization");
  if (tokenWithPrefixFromRequest) {
    const cleanedToken = tokenWithPrefixFromRequest.replace("Token ", "");
    return cleanedToken === expectedToken;
  } else {
    return false;
  }
};

export const tokenExists = (request: NextRequest) => {
  return request.headers.get("authorization");
};
