import { NextResponse } from "next/server";

export enum ResponseCode {
  SUCCESS = 1000,
  ERROR = 3000,
}

export interface ResponseBody {
  data?: object;
  status_code: ResponseCode;
  status_message: string;
  timestamp: string;
}

const response = (status_code: number, message: string, data?: object) => {
  const responseBody: ResponseBody = {
    status_code: status_code,
    status_message: message,
    timestamp: new Date().toISOString(),
  };

  if (!data) {
    return NextResponse.json(responseBody);
  }
  responseBody.data = data;
  return NextResponse.json(responseBody);
};

export const SuccessResponse = (message: string, data?: object) => {
  return response(ResponseCode.SUCCESS, message, data);
};

export const ErrorResponse = (message: string, data?: object) => {
  return response(ResponseCode.ERROR, message, data);
};
