import { z } from "zod";

const coordinatesSchema = z.object({
  latitude: z.string(),
  longitude: z.string(),
});

export const ConnectorSchema = z.object({
  id: z.string(),
  standard: z.string(),
  format: z.string(),
  power_type: z.string(),
  max_voltage: z.number(),
  max_amperage: z.number(),
  max_electric_power: z.number().nullable(),
  last_updated: z.string(),
  tariff_ids: z.array(z.string()).default([]),
});

export const EvseSchema = z.object({
  uid: z.string(),
  evse_id: z.string().nullable().default(""),
  status: z.string(),
  capabilities: z.array(z.string()),
  connectors: z.array(ConnectorSchema),
  coordinates: coordinatesSchema,
  physical_reference: z.string().nullable(),
  last_updated: z.string(),
});

const operatorSchema = z.optional(
  z.object({
    name: z.string(),
  })
);

const ownerSchema = z.optional(
  z.object({
    name: z.string(),
  })
);

export const LocationSchema = z.object({
  id: z.string(),
  country_code: z.string(),
  party_id: z.string(),
  publish: z.boolean(),
  publish_allowed_to: z.array(z.any()),
  name: z.string(),
  address: z.string(),
  city: z.string(),
  postal_code: z.string(),
  country: z.string(),
  coordinates: coordinatesSchema,
  parking_type: z.optional(z.string()),
  evses: z.array(EvseSchema),
  operator: operatorSchema,
  suboperator: operatorSchema,
  owner: ownerSchema,
  time_zone: z.string(),
  charging_when_closed: z.boolean(),
  last_updated: z.string(),
});

export const LocationsResponseSchema = z.object({
  status_code: z.number(),
  status_message: z.string(),
  timestamp: z.string(),
  data: z.array(LocationSchema),
});

export const LocationResponseSchema = z.object({
  status_code: z.number(),
  status_message: z.string(),
  timestamp: z.string(),
  data: LocationSchema,
});
