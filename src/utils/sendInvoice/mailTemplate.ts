import { type Invoice } from "../../../prisma/client";
import { InvoiceSchema } from "~/utils/invoicePdf";
import { convertUtcToCet } from "~/utils/date/date";
import { prisma } from "~/server/db";

interface Props {
  invoice: Invoice;
}

export const getInvoiceMailMessage = ({ invoice }: Props): [string, string] => {
  const validateInvoice = InvoiceSchema.safeParse(invoice);
  if (!validateInvoice.success) {
    throw new Error("Invoice not valid");
  }

  const subjectDe = `Rechnung für Ad Hoc Ladevorgang von Eulektro GmBH – Rechnungsnummer ${validateInvoice.data.invoice_number}`;
  const invoiceMailDe =
    `Sehr geehrte Damen und Herren,\n` +
    `\n` +
    `Im Anhang dieser E-Mail finden Sie die Rechnung für die Nutzung der Ladeinfrastruktur der im Zeitraum vom ${validateInvoice.data.service_period_from.toLocaleDateString(
      "de-DE"
    )} bis ${validateInvoice.data.service_period_to.toLocaleDateString(
      "de-DE"
    )}\n` +
    `\n` +
    `Rechnungsdetails:\n` +
    `Rechnungsnummer: ${validateInvoice.data.invoice_number}\n` +
    `Rechnungsdatum: ${validateInvoice.data.invoice_date?.toLocaleDateString(
      "de-DE",
      {
        timeZone: "Europe/Berlin",
      }
    )}\n` +
    "\nDiese Rechnung wurde bereits Bezahlt:\n" +
    `\n` +
    `\n` +
    `Für eventuelle Rückfragen zur Rechnung stehen wir Ihnen gerne zur Verfügung.\n` +
    `\n` +
    `Vielen Dank für Ihr Vertrauen in unsere Ladeinfrastruktur.` +
    `\n` +
    `Mit freundlichen Grüßen,\n` +
    `\n` +
    `Team Eulektro (DE*EUL)\n` +
    `Eulektro GmbH\n\n` +
    `Werderstraße 69\n` +
    `28199 Bremen\n` +
    `+49 421 17 51 28 90\n` +
    `<EMAIL>` +
    `\n\n\n\n`;

  return [subjectDe, invoiceMailDe];
};
