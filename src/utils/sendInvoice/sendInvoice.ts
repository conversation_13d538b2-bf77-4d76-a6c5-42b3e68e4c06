import fs from "fs";
import nodemailer from "nodemailer";
import { getInvoiceMailMessage } from "~/utils/sendInvoice/mailTemplate";
import {
  type Invoice,
  InvoiceStatusEnum,
  Prisma,
} from "../../../prisma/client";
import { env } from "~/env.mjs";
import { prisma } from "~/server/db";
import { SendErrorMessage } from "~/utils/chat/sendMessage";
import JsonObject = Prisma.JsonObject;

export const sendInvoice = (invoice: Invoice) => {
  if (invoice.status == InvoiceStatusEnum.DRAFT || !invoice.mailToSend) {
    return false;
  }

  const mail_transporter = nodemailer.createTransport({
    port: 465,
    host: env.EMAIL_SERVER_HOST,
    auth: {
      user: env.EMAIL_SERVER_USER,
      pass: env.EMAIL_SERVER_PASSWORD,
    },
    secure: true,
  });

  const attachments = invoice.files.map((file) => {
    return {
      filename: file.name,
      content: fs.createReadStream(file.path),
      contentType: file?.application_type || "application/octet-stream",
    };
  });

  const [subject, message] = getInvoiceMailMessage({
    invoice: invoice,
  });

  const invoiceMailData = {
    from: process.env.EMAIL_FROM,
    to: invoice.mailToSend,
    replyTo: `<EMAIL>`,
    bcc: "<EMAIL>",
    subject: subject,
    text: message,
    attachments: attachments.filter((x) => x.contentType == "application/pdf"),
  };

  mail_transporter
    .sendMail(invoiceMailData)
    .then(async (res) => {
      await prisma.invoice.update({
        where: {
          id: invoice.id,
        },
        data: {
          sentAsEmail: true,
        },
      });
      const metaData = invoice.metadata as JsonObject;
      const paymentIntentId = metaData?.paymentIntent ?? "";

      if (paymentIntentId) {
        await prisma.paymentIntent.update({
          where: { id: paymentIntentId.toString() },
          data: { invoiceMail: invoice.mailToSend },
        });
      }
    })
    .catch(async (err) => {
      await SendErrorMessage(`Fehler beim Email Versand `);
    });
};
