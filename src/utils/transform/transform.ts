export const transformSlugToEvseId = (input: string): string => {
  const parts = input.split("_");

  if (parts.length < 3) {
    throw new Error("Invalid input format");
  }

  if (parts.length < 4) {
    const formattedNumber = parts[1]?.padStart(4, "0");
    if (formattedNumber && parts[0] && parts[2]) {
      return `DE*${parts[0]}*E${formattedNumber}*${parts[2]}`;
    }
  }

  return parts.join("*");
};

// DE*EUL*E0001*01 to DE_EUL_E0001_01
export const transformEvseToEvseSlugId = (evseId: string): string => {
  // Teilt den String an den Sternchen
  const parts = evseId.split("*");

  // Fügt Unterstriche zwischen den Teilen hinzu
  const chargePointId = parts.join("_");

  // evse DE*EUL*EHAN0001*01 oder DE*EUL*0001*01
  // transform to
  // chargePointId DE_EUL_EHAN0001_01 oder DE_EUL_E0001_01

  // Gibt die generierte ChargePointID zurück
  return chargePointId;
};
