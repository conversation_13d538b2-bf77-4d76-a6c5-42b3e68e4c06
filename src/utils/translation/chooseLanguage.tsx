"use client";
import Image from "next/image";
import { localeMetaData, locales } from "i18n";
import { useRouter, useSearchParams } from "next/navigation";
import { usePathname } from "next/navigation";
import { useState } from "react";

import { type Theme } from "~/styles/oucolors/whiteLabelColors";

const LanguageSelector = ({ theme }: { theme: Theme }) => {
  const router = useRouter();
  const pathName = usePathname();
  const [open, setOpen] = useState(false);
  const searchParams = useSearchParams();
  const lang_params = searchParams.get("lang") ?? "de";

  const [selectedLanguage, setSelectedLanguage] = useState<string>(lang_params);

  const redirectedPathName = () => {
    if (!pathName) return "/";

    // in case we already have p arams, add lang param
    if (searchParams && searchParams.toString()) {
      const newParams = new URLSearchParams(searchParams.toString());
      newParams.set("lang", selectedLanguage);
      return pathName + `?${newParams.toString()}`;
    }
    // if no params are set, just set lang param
    return pathName + "?" + "lang=" + selectedLanguage;
  };

  return (
    <>
      <div
        className={"absolute left-5 top-4 flex flex-row gap-2 text-secondary"}
        onClick={() => {
          setOpen(!open);
        }}
      >
        <Image
          alt={"close"}
          src={theme?.globe ?? "/commonImages/globe.svg"}
          className={"cursor-pointer"}
          width={25}
          height={25}
        />
        <span>{localeMetaData[lang_params]?.name ?? "Default"}</span>
      </div>
      {open && (
        <div
          className={
            "h-content fixed bottom-0 left-0 z-10 flex h-full w-full  flex-col rounded-t-3xl bg-white pb-8 transition-all duration-500 ease-in-out"
          }
        >
          <div
            className={
              "flex h-full flex-col justify-between gap-5 px-6 text-2xl text-primary"
            }
          >
            <div
              className={
                "mt-10 flex  h-full max-h-[50vh]  flex-grow flex-col gap-5 overflow-y-auto"
              }
            >
              <div className={"flex-shrink-0 font-sansPro font-bold"}>
                Select language
                <div
                  onClick={() => {
                    setOpen(false);
                  }}
                  className={"absolute right-5 top-10"}
                >
                  <Image
                    alt={"close"}
                    src={"/commonImages/close.svg"}
                    className={"cursor-pointer"}
                    width={25}
                    height={25}
                  />
                </div>
              </div>

              <ul className={"flex flex-grow flex-col gap-3 overflow-y-auto "}>
                {locales.map((locale) => (
                  <li key={locale} className={"flex items-center"}>
                    <label className={"flex items-center"}>
                      <input
                        className={
                          "font-3xl  text-grey-700 mr-3 h-7 w-7 items-center"
                        }
                        type="radio"
                        name="language"
                        checked={locale === selectedLanguage}
                        value={locale}
                        onChange={(event) =>
                          setSelectedLanguage(event.target.value)
                        }
                      />
                      {localeMetaData[locale]?.flag}{" "}
                      {localeMetaData[locale]?.name}
                    </label>
                     
                  </li>
                ))}
              </ul>
            </div>
          </div>

          <div className={" px-5 "}>
            <button
              onClick={() => {
                router.push(redirectedPathName());
                router.refresh();
                setOpen(false);
              }}
              className={
                "w-full rounded-xl bg-primary p-3 text-2xl font-bold text-secondary "
              }
            >
              Apply
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default LanguageSelector;
