# EulHub Nextjs Test Server

Dieses Dokument beschreibt, wie Sie den Systemd-Service für den EulHub Nextjs Test Server konfigurieren und verwenden können.

## Anlegen der Service-Datei

Die Service-Datei sollte in einem der folgenden Verzeichnisse erstellt werden:

- `/etc/systemd/system/`: Für system-spezifische Service-Dateien, die vom Systemadministrator erstellt wurden.
- `/lib/systemd/system/` oder `/usr/lib/systemd/system/`: Für Service-Dateien, die mit installierten Paketen geliefert werden.
- `/run/systemd/system/`: Für temporäre Service-Dateien, die nur für die aktuelle Sitzung gelten.

In den meisten Fällen wird die Service-Datei im Verzeichnis `/etc/systemd/system/` erstellt. Erstellen Sie eine Datei namens `eulektro.service` mit folgendem Inhalt:

```ini
[Unit]
Description=EulHub Nextjs Test Server
After=multi-user.target

[Service]
WorkingDirectory=/var/www/vhosts/eulektro.de/test.adhoc.eulektro.de/eulhub
User=eulektro
Type=simple
Restart=always
ExecStart=/var/www/vhosts/eulektro.de/test.adhoc.eulektro.de/eulhub/node_modules/.bin/next start
WantedBy=multi-user.target
EnvironmentFile=/var/www/vhosts/eulektro.de/test.adhoc.eulektro.de/build/env

[Install]
WantedBy=multi-user.target
```

