import { type Config } from "tailwindcss";

export default {
  content: ["./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    screens: {
      "2xs": "0px",
      xs: "390px",
      sm: "640px",
      // => @media (min-width: 640px) { ... }

      md: "768px",
      // => @media (min-width: 768px) { ... }

      lg: "1024px",
      // => @media (min-width: 1024px) { ... }

      xl: "1280px",
      // => @media (min-width: 1280px) { ... }

      "2xl": "1536px",
      // => @media (min-width: 1536px) { ... }
    },
    fontSize: {
      "2xs": "0.75rem",
      xs: "0.8125rem",
      sm: "0.875rem",
      base: "1rem",
      lg: "1.125rem",
      xl: "1.25rem",
      "2xl": "1.5rem",
      "3xl": "1.875rem",
      "4xl": "2.25rem",
      "5xl": "3rem",
      "6xl": "3.75rem",
      "7xl": "4.5rem",
    },
    extend: {
      fontFamily: {
        sansPro: "var(--font-sansPro)",
        mont: "var(--font-mont)",
      },
      backgroundImage: {
        //adhocStep5: "url('/adhoc/step5/bg.svg')",
        adhocStep5: "var(--bgurl)",
      },
      backgroundColor: {
        primary: "var(--color-primary)",
        secondary: "var(--color-secondary)",
        main: "var(--color-main)",
      },
      textColor: {
        primary: "var(--color-primary-text)",
        secondary: "var(--color-secondary-text)",
      },
      colors: {
        eul: { "100": "#ECECE7" },
        elm: {
          "50": "#effbfc",
          "100": "#d6f4f7",
          "200": "#b2e9ef",
          "300": "#7dd8e3",
          "400": "#40bed0",
          "500": "#24a1b6",
          "600": "#218399",
          "700": "#21697c",
          "800": "#235767",
          "900": "#214958",
          "950": "#112f3b",
        },
      },
    },
  },
  plugins: [],
} satisfies Config;
